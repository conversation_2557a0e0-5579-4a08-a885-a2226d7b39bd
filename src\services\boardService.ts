import { 
  Board, 
  BoardService, 
  BoardSavePayload, 
  BoardSaveResponse, 
  ElementAction, 
  BoardItem, 
  Connection,
  PenStroke
} from '../types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Implementation of the BoardService interface
 */
export class BoardServiceImpl implements BoardService {
  private getHeaders(): HeadersInit {
    return {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Construct absolute URL for server-side fetch calls
   */
  private getApiUrl(path: string): string {
    // Check if we're in a server environment (Node.js)
    if (typeof window === 'undefined') {
      // Server-side: construct absolute URL
      const baseUrl = process.env.VERCEL_URL
        ? `https://${process.env.VERCEL_URL}`
        : 'http://localhost:3000';
      return `${baseUrl}${path}`;
    }
    // Client-side: use relative URL
    return path;
  }

  /**
   * Get a board by ID
   */
  async getPublicBoard(boardId: string): Promise<Board> {
    try {
      // Use the public board API endpoint for public board view
      const response = await fetch(this.getApiUrl(`/api/board/public?boardId=${boardId}`));
      
      if (!response.ok) {
        let errorMsg = 'Failed to load board';
        try {
            const errorData = await response.json();
            errorMsg = errorData.message || errorMsg;
        } catch (parseError) { /* Ignore if body isn't JSON */ }
        throw new Error(errorMsg);
      }
      
      const data = await response.json();
      
      // Ensure connections are properly structured with fromId, toId
      let processedConnections = [];
      if (Array.isArray(data.connections)) {
        processedConnections = data.connections.map((conn: any) => ({
          id: conn.id,
          fromId: conn.fromId || conn.from_element_id,
          toId: conn.toId || conn.to_element_id,
          label: conn.label || '',
          type: conn.type || conn.connection_type || 'default'
        }));
      }
      
      // Create sharing record based on public status
      const sharingRecord = {
        id: uuidv4(),
        board_id: data.id,
        public_board: true,
        likes: data.likes || 0,
        created_at: data.publicSince || new Date().toISOString(),
        updated_at: data.publicSince || new Date().toISOString()
      };
      
      // Map the API response to our internal Board type
      return {
        id: data.id,
        name: data.name || 'Untitled board',
        elements: data.elements || [],
        connections: processedConnections,
        strokes: data.strokes || [],
        sharing: [sharingRecord],
        createdAt: data.createdAt ? new Date(data.createdAt) : undefined,
        updatedAt: data.updatedAt ? new Date(data.updatedAt) : undefined,
        userId: data.userId
      };
    } catch (error) {
      console.error('Error loading board:', error);
      throw error;
    }
  }

  async getBoard(boardId: string): Promise<Board> {
    try {
      const response = await fetch(this.getApiUrl(`/api/board/${boardId}`));
      
      if (!response.ok) {
        let errorMsg = 'Failed to load board';
        try {
            const errorData = await response.json();
            errorMsg = errorData.message || errorMsg;
        } catch (parseError) { /* Ignore if body isn't JSON */ }
        throw new Error(errorMsg);
      }
      
      const data = await response.json();
      console.log("API response data:", data);
      
      // Map the API response to our internal Board type
      return {
        id: data.id,
        name: data.boardName || 'Untitled board',
        elements: data.elements || [],
        connections: data.connections || [],
        strokes: data.strokes || [],
        sharing: data.sharing || [],
        liked: data.liked,
        createdAt: data.createdAt ? new Date(data.createdAt) : undefined,
        updatedAt: data.updatedAt ? new Date(data.updatedAt) : undefined,
        userId: data.userId
      };
    } catch (error) {
      console.error('Error loading board:', error);
      throw error;
    }
  }

  /**
   * Save a board (create or update)
   */
  async saveBoard(payload: BoardSavePayload): Promise<BoardSaveResponse> {
    try {
      const response = await fetch(this.getApiUrl('/api/board/save'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...payload,
          elements: payload.elements || [],
          connections: payload.connections || [],
          strokes: payload.strokes || [],
          sharing: payload.sharing || []
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to save board');
      }
      
      return {
        boardId: data.boardId,
        message: data.message,
        success: true
      };
    } catch (error) {
      console.error('Error saving board:', error);
      throw error;
    }
  }

  /**
   * Update a board element (add, update, delete)
   */
  async updateElement(
    boardId: string, 
    element: BoardItem, 
    action: ElementAction
  ): Promise<{ element?: BoardItem }> {
    try {
      const response = await fetch(this.getApiUrl('/api/board/update-elements'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          boardId,
          element,
          action
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to save element change');
      }
      
      return { element: data.element };
    } catch (error) {
      console.error('Error saving element change:', error);
      throw error;
    }
  }

  /**
   * Update multiple board elements in a batch (add only for now)
   */
  async updateBatchElements(
    boardId: string, 
    elements: BoardItem[], 
    action: ElementAction
  ): Promise<{ elements?: BoardItem[] }> {
    try {
      // Prepare elements for the API call.
      // Specifically, if an article has file_url === null, convert it to undefined.
      const processedElements = elements.map(el => {
        if (el.type === 'article' && el.file_url === null) {
          // Create a new object without file_url or set it to undefined
          // to avoid sending "file_url": null if the API misinterprets it.
          const { file_url, ...rest } = el;
          return { ...rest, file_url: undefined }; 
        }
        return el;
      });

      const response = await fetch(this.getApiUrl('/api/board/update-elements'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          boardId,
          elements: processedElements, // Send the processed elements
          action,
          isBatch: true
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to save batch element changes');
      }
      
      return { elements: data.elements };
    } catch (error) {
      console.error('Error saving batch element changes:', error);
      throw error;
    }
  }

  /**
   * Update a connection (add, update, delete)
   */
  async updateConnection(
    boardId: string, 
    connection: Connection, 
    action: ElementAction
  ): Promise<{ connection?: Connection }> {
    try {
      const response = await fetch(this.getApiUrl('/api/board/update-connection'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          boardId,
          connection,
          action
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to save connection change');
      }
      
      return { connection: data.connection };
    } catch (error) {
      console.error('Error saving connection change:', error);
      throw error;
    }
  }

  /**
   * Update multiple connections in a batch (add only for now)
   */
  async updateBatchConnections(
    boardId: string, 
    connections: Connection[], 
    action: ElementAction
  ): Promise<{ connections?: Connection[] }> {
    try {
      // Reuse the existing API endpoint but send batch data
      const response = await fetch(this.getApiUrl('/api/board/update-connection'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          boardId,
          connections,
          action,
          isBatch: true
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to save batch connection changes');
      }
      
      return { connections: data.connections };
    } catch (error) {
      console.error('Error saving batch connection changes:', error);
      throw error;
    }
  }

  /**
   * Delete a board
   */
  async deleteBoard(boardId: string): Promise<boolean> {
    try {
      const response = await fetch(this.getApiUrl(`/api/board/${boardId}`));
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Failed to delete board');
      }
      
      return true;
    } catch (error) {
      console.error('Error deleting board:', error);
      throw error;
    }
  }

  /**
   * List all boards for the current user
   */
  async listBoards(): Promise<BoardItem[]> {
    try {
      const response = await fetch(this.getApiUrl('/api/board/user-boards'));
      
      if (!response.ok) {
        let errorMsg = 'Failed to fetch boards';
        try {
            const errorData = await response.json();
            errorMsg = errorData.message || errorMsg;
        } catch (parseError) { /* Ignore if body isn't JSON */ }
        throw new Error(errorMsg);
      }
      
      const data = await response.json();
      const allBoards = [
          ...(data.userBoards || []),
          ...(data.sharedBoards || [])
      ];
      return allBoards;
    } catch (error) {
      console.error('Error fetching boards:', error);
      throw error;
    }
  }

  /**
   * Update only the strokes for a board (for drawings)
   * This avoids overwriting other board data when only strokes change
   */
  async updateStrokesOnly(boardId: string, strokes: PenStroke[]): Promise<void> {
    try {
      const response = await fetch(this.getApiUrl('/api/board/update-strokes'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          boardId,
          strokes
        }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Failed to update strokes');
      }
    } catch (error) {
      console.error('Error updating strokes:', error);
      throw error;
    }
  }
}

/**
 * Singleton instance of the board service
 */
export const boardService = new BoardServiceImpl(); 