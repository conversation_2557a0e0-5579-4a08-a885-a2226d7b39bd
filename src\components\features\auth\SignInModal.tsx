'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/ui/modal';

interface SignInModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  onVerificationNeeded: (email: string, userId?: string) => void;
  onSignUpClick?: () => void;
}

interface SignInResult {
  needsVerification?: boolean;
  userId?: string;
  verificationSentAt?: string;
}

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

// Declare google accounts types for TypeScript
declare global {
  interface Window {
    googleGsiClientLoaded?: boolean;
    google?: {
      accounts: {
        id: {
          initialize: (config: {
            client_id: string;
            callback: (response: CredentialResponse) => void;
          }) => void;
          renderButton: (
            parent: HTMLElement,
            options: {
              theme?: 'outline' | 'filled_blue' | 'filled_black';
              size?: 'large' | 'medium' | 'small';
              type?: 'standard' | 'icon';
              shape?: 'rectangular' | 'pill' | 'circle' | 'square';
              text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
              logo_alignment?: 'left' | 'center';
              width?: string; // e.g., '250px'
            }
          ) => void;
          prompt: (momentNotification?: (notification: any) => void) => void;
        };
      };
    };
  }
}

interface CredentialResponse {
  credential?: string;
  select_by?: string;
  // ... other potential fields in the response
}

export function SignInModal({ isOpen, onClose, onSuccess, onVerificationNeeded, onSignUpClick }: SignInModalProps) {
  const { signIn, signInWithGoogle, isAuthenticated, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<FormErrors>({});
  const googleButtonContainerRef = useRef<HTMLDivElement>(null);
  const [isGsiButtonRendered, setIsGsiButtonRendered] = useState(false);
  const successHandledRef = useRef(false);

  // Monitor authentication state and close modal when user becomes authenticated
  useEffect(() => {
    if (isOpen && isAuthenticated && user && !isLoading && !successHandledRef.current) {
      console.log("[SignInModal] User authenticated and not loading, closing modal automatically");
      successHandledRef.current = true;
      if (onSuccess) onSuccess();
      onClose();
    }
  }, [isOpen, isAuthenticated, user, isLoading, onSuccess, onClose]);

  // Reset success handled flag when modal opens
  useEffect(() => {
    if (isOpen) {
      successHandledRef.current = false;
    }
  }, [isOpen]);

  const validateForm = () => {
    const newErrors: FormErrors = {};
    if (!email) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(email)) newErrors.email = 'Please enter a valid email address';
    if (!password) newErrors.password = 'Password is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsLoading(true);
    setErrors({});
    try {
      const result: SignInResult = await signIn(email, password);
      if (result.needsVerification) {
        onVerificationNeeded(email, result.userId);
      } else {
        if (!successHandledRef.current) {
          successHandledRef.current = true;
          if (onSuccess) onSuccess();
          onClose();
        }
      }
    } catch (error: any) {
      console.error('Sign in error:', error);
      setErrors({ general: error.message.includes('Invalid login credentials') ? 'Invalid email or password' : error.message || 'Failed to sign in' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleCredentialResponse = async (response: CredentialResponse) => {
    console.log("Google Credential Response:", response);
    if (response.credential) {
      setIsLoading(true);
      setErrors({});

      try {
        console.log("[SignInModal] Calling signInWithGoogle...");
        await signInWithGoogle(response.credential);
        console.log("[SignInModal] signInWithGoogle resolved successfully");

        // Always call success and close modal after successful Google sign-in
        console.log("[SignInModal] Google sign-in successful, calling onSuccess and closing modal");
        setIsLoading(false);
        if (!successHandledRef.current) {
          successHandledRef.current = true;
          if (onSuccess) onSuccess();
          onClose(); // Close the modal after successful sign-in
        }
      } catch (error: any) {
        console.error('Google sign in error after GSI callback:', error);

        // Check if this is a timeout error where auth might have succeeded
        if (error.message?.includes('timed out')) {
          // Don't show error immediately - the page might be reloading
          console.log('[SignInModal] Sign-in timed out, page may reload automatically');
          return; // Don't update loading state - let the reload happen
        }

        // Update error state
        setErrors({ general: error.message || 'Failed to sign in with Google' });
        setIsLoading(false);
      }
    } else {
      console.error('Google GSI: No credential in response');
      setErrors({ general: 'Google Sign-In failed. Please try again.'});
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log('SignInModal useEffect triggered. isOpen:', isOpen);

    if (!isOpen) {
      if (googleButtonContainerRef.current) {
        googleButtonContainerRef.current.innerHTML = '';
      }
      setIsGsiButtonRendered(false);
      // Reset loading state when modal closes to prevent stuck loading states
      setIsLoading(false);
      setErrors({});
      // Reset form fields
      setEmail('');
      setPassword('');
      // Potentially cancel any running interval if one was stored on the component or ref
      return;
    }

    // Define attemptRender first, as it will be used by the interval
    const attemptRender = () => {
      if (typeof window === 'undefined' || !googleButtonContainerRef.current) {
        console.log(
          'SignInModal (attemptRender): Waiting for window or googleButtonContainerRef.current.',
          {
            isWindowUndefined: typeof window === 'undefined',
            isRefCurrentNull: !googleButtonContainerRef.current,
          }
        );
        return false; // Conditions not met
      }

      if (!window.googleGsiClientLoaded || !(window.google && window.google.accounts && window.google.accounts.id)) {
        console.log('SignInModal (attemptRender): Google GSI script or accounts object not ready yet.');
        return false; // GSI not ready
      }

      if (isGsiButtonRendered || googleButtonContainerRef.current!.childElementCount > 0) {
         console.log('SignInModal (attemptRender): GSI button process already initiated or container not empty.');
         return true; // Already rendered or processing was attempted.
      }
      
      console.log("SignInModal (attemptRender): Attempting to initialize and render Google Sign-In button.");
      try {
        const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
        if (!clientId) {
          console.error('SignInModal (attemptRender): NEXT_PUBLIC_GOOGLE_CLIENT_ID is not set.');
          setErrors({ general: 'Google Sign-In is not configured.' });
          return true; // Stop further attempts
        }
        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: handleGoogleCredentialResponse,
        });
        if (googleButtonContainerRef.current) {
            googleButtonContainerRef.current.innerHTML = ''; 
        }
        window.google.accounts.id.renderButton(
          googleButtonContainerRef.current!,
          { theme: 'outline', size: 'large', type: 'standard', text: 'signin_with', width: googleButtonContainerRef.current!.offsetWidth ? `${googleButtonContainerRef.current!.offsetWidth}px` : '100%', shape: 'rectangular' }
        );
        setIsGsiButtonRendered(true);
        console.log('SignInModal (attemptRender): Google Sign-In button render process initiated.');
      } catch (error) {
        console.error('SignInModal (attemptRender): Error rendering Google Sign-In button:', error);
        setErrors({ general: 'Could not load Google Sign-In. Please try again later.'});
        return true; // Indicate an error occurred
      }
      return true; // Successfully initiated or error handled
    };

    // If initial attempt is successful, no interval needed.
    // Otherwise, set up an interval to keep trying.
    if (!attemptRender()) {
      console.log("SignInModal: Initial render attempt failed, setting up interval.");
      const intervalId = setInterval(() => {
        console.log("SignInModal: Interval check for GSI readiness.");
        if (attemptRender()) {
          console.log("SignInModal: GSI ready via interval, clearing interval.");
          clearInterval(intervalId);
        }
      }, 300);

      return () => {
        console.log("SignInModal: Clearing GSI readiness interval (useEffect cleanup).");
        clearInterval(intervalId);
      };
    } else {
      console.log("SignInModal: Initial render attempt successful, no interval needed.");
    }
  }, [isOpen]); // Dependency remains isOpen

  const inputClassName = (error?: string) => 
    error 
      ? "mt-1 block w-full rounded-md border px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 border-red-500 bg-red-900/10 focus:border-red-500 focus:ring-red-500" 
      : "mt-1 block w-full rounded-md border px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 border-white/10 bg-black/20 focus:border-noir-accent focus:ring-noir-accent";

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Welcome Back">
      <p className="text-sm text-gray-400 text-center mb-8">
        Sign in to continue your investigation
      </p>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {errors.general && (
          <div className="text-red-500 text-sm text-center bg-red-500/10 p-2 rounded-md border border-red-500/20">
            {errors.general}
          </div>
        )}

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-300">
            Email or Username
          </label>
          <input
            id="email"
            type="text"
            required
            className={inputClassName(errors.email)}
            placeholder="Enter your email or username"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              setErrors(prev => ({ ...prev, email: undefined, general: undefined }));
            }}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-500">{errors.email}</p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-300">
            Password
          </label>
          <input
            id="password"
            type="password"
            required
            className={inputClassName(errors.password)}
            placeholder="Enter your password"
            value={password}
            onChange={(e) => {
              setPassword(e.target.value);
              setErrors(prev => ({ ...prev, password: undefined, general: undefined }));
            }}
          />
          {errors.password && (
            <p className="mt-1 text-sm text-red-500">{errors.password}</p>
          )}
        </div>

        <div>
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-noir-accent hover:bg-noir-accent/90 relative"
          >
            {isLoading && !errors.general ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              'Sign In'
            )}
          </Button>
        </div>
      </form>

      <div className="relative my-6">
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-noir-primary px-2 text-gray-400">
            Or
          </span>
        </div>
      </div>

      {/* Google Sign-In Button Container */}
      <div ref={googleButtonContainerRef} id="gsi-signin-button-container" className="w-full flex justify-center"></div>
      {/* Show a loader if GSI button is loading and not yet rendered, and no general error */}
      {isLoading && !isGsiButtonRendered && !errors.general && (
        <div className="flex justify-center items-center h-[50px]">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
        </div>
      )}

      <div className="mt-4 text-center">
        <button
          type="button"
          className="text-sm text-gray-400 hover:text-white"
          onClick={onSignUpClick || (() => {
            onClose();
            onVerificationNeeded(email);
          })}
        >
          Don't have an account? Sign up
        </button>
      </div>
    </Modal>
  );
} 