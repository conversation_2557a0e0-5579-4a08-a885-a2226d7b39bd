import React from 'react';
import { motion } from 'framer-motion';

export interface OperationFeedbackOptions {
  /** Duration of the feedback animation (default: 2000ms) */
  duration?: number;
  /** Type of operation for appropriate styling */
  operationType?: 'create' | 'update' | 'delete' | 'connect';
  /** Custom message to display */
  message?: string;
  /** Position to show the feedback */
  position?: { x: number; y: number };
  /** Whether to show a loading spinner */
  showSpinner?: boolean;
}

/**
 * Create a floating operation feedback component
 */
export function createOperationFeedback(
  operationType: 'create' | 'update' | 'delete' | 'connect',
  message?: string
) {
  const getOperationConfig = (type: string) => {
    switch (type) {
      case 'create':
        return {
          icon: '✨',
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          defaultMessage: 'Creating elements...'
        };
      case 'update':
        return {
          icon: '📝',
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          defaultMessage: 'Updating element...'
        };
      case 'delete':
        return {
          icon: '🗑️',
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          defaultMessage: 'Deleting element...'
        };
      case 'connect':
        return {
          icon: '🔗',
          color: 'text-purple-500',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200',
          defaultMessage: 'Creating connection...'
        };
      default:
        return {
          icon: '⚡',
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          defaultMessage: 'Processing...'
        };
    }
  };

  const config = getOperationConfig(operationType);
  
  return {
    config,
    message: message || config.defaultMessage
  };
}

/**
 * Animation variants for operation feedback
 */
export const operationFeedbackVariants = {
  initial: {
    opacity: 0,
    scale: 0.8,
    y: 20
  },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: 'easeOut'
    }
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    y: -10,
    transition: {
      duration: 0.2,
      ease: 'easeIn'
    }
  }
};

/**
 * Deletion animation variants for elements
 */
export const deletionAnimationVariants = {
  initial: {
    opacity: 1,
    scale: 1
  },
  deleting: {
    opacity: 0.5,
    scale: 0.95,
    transition: {
      duration: 0.3,
      ease: 'easeInOut'
    }
  },
  deleted: {
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.4,
      ease: 'easeIn'
    }
  }
};

/**
 * Connection creation animation variants
 */
export const connectionCreationVariants = {
  initial: {
    pathLength: 0,
    opacity: 0
  },
  creating: {
    pathLength: 0.5,
    opacity: 0.7,
    transition: {
      duration: 0.5,
      ease: 'easeInOut'
    }
  },
  created: {
    pathLength: 1,
    opacity: 1,
    transition: {
      duration: 0.4,
      ease: 'easeOut'
    }
  }
};

/**
 * Update animation variants for elements
 */
export const updateAnimationVariants = {
  initial: {
    scale: 1,
    boxShadow: 'none'
  },
  updating: {
    scale: 1.05,
    boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.3)',
    transition: {
      duration: 0.3,
      ease: 'easeOut'
    }
  },
  updated: {
    scale: 1,
    boxShadow: '0 0 0 2px rgba(34, 197, 94, 0.6), 0 0 15px rgba(34, 197, 94, 0.4)',
    transition: {
      duration: 0.4,
      ease: 'easeInOut'
    }
  },
  complete: {
    scale: 1,
    boxShadow: 'none',
    transition: {
      duration: 0.5,
      ease: 'easeOut'
    }
  }
};

/**
 * Pulse animation for highlighting elements
 */
export const pulseHighlightVariants = {
  initial: {
    boxShadow: 'none'
  },
  pulse: {
    boxShadow: [
      'none',
      '0 0 0 4px rgba(214, 173, 96, 0.4), 0 0 20px rgba(214, 173, 96, 0.3)',
      'none'
    ],
    transition: {
      duration: 1.5,
      repeat: 2,
      ease: 'easeInOut'
    }
  }
};

/**
 * Loading spinner component for operations
 */
export const OperationSpinner = ({ size = 16, className = '' }: { size?: number; className?: string }) => (
  <motion.div
    className={`inline-block ${className}`}
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
  >
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeDasharray="32"
        strokeDashoffset="32"
      >
        <animate
          attributeName="stroke-dasharray"
          dur="2s"
          values="0 32;16 16;0 32;0 32"
          repeatCount="indefinite"
        />
        <animate
          attributeName="stroke-dashoffset"
          dur="2s"
          values="0;-16;-32;-32"
          repeatCount="indefinite"
        />
      </circle>
    </svg>
  </motion.div>
);

/**
 * Progress indicator for batch operations
 */
export interface BatchOperationProgress {
  current: number;
  total: number;
  operationType: string;
}

export const BatchProgressIndicator = ({ 
  progress, 
  className = '' 
}: { 
  progress: BatchOperationProgress; 
  className?: string; 
}) => (
  <motion.div
    className={`flex items-center gap-2 ${className}`}
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -10 }}
  >
    <OperationSpinner size={14} />
    <span className="text-sm text-gray-600">
      {progress.operationType} {progress.current} of {progress.total}...
    </span>
    <div className="w-20 h-1 bg-gray-200 rounded-full overflow-hidden">
      <motion.div
        className="h-full bg-blue-500 rounded-full"
        initial={{ width: 0 }}
        animate={{ width: `${(progress.current / progress.total) * 100}%` }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
      />
    </div>
  </motion.div>
);

/**
 * Toast-style operation feedback
 */
export const OperationToast = ({
  operationType,
  message,
  isVisible,
  onClose
}: {
  operationType: 'create' | 'update' | 'delete' | 'connect';
  message?: string;
  isVisible: boolean;
  onClose?: () => void;
}) => {
  const { config, message: defaultMessage } = createOperationFeedback(operationType, message);

  return (
    <motion.div
      className={`fixed top-4 right-4 z-50 ${config.bgColor} ${config.borderColor} border rounded-lg shadow-lg p-3 max-w-sm`}
      variants={operationFeedbackVariants}
      initial="initial"
      animate={isVisible ? "animate" : "exit"}
      exit="exit"
    >
      <div className="flex items-center gap-2">
        <span className="text-lg">{config.icon}</span>
        <span className={`text-sm font-medium ${config.color}`}>
          {message || defaultMessage}
        </span>
        <OperationSpinner size={14} className={config.color} />
        {onClose && (
          <button
            onClick={onClose}
            className="ml-auto text-gray-400 hover:text-gray-600 transition-colors"
          >
            ×
          </button>
        )}
      </div>
    </motion.div>
  );
}; 