import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  AIMessage, 
  StructuredAIResponse, 
  ToolCall, 
  ToolResult, 
  ConversationState,
  AI_RESPONSE_SCHEMA 
} from '@/types/ai-messages';
import { AI_CONFIG } from '@/config/ai-limits';

// Updated system prompt for structured JSON responses with enhanced connection guidance
const SYSTEM_PROMPT = `You are an AI Research Assistant for the Detective Board application. You help users research topics and manage their detective-style boards.

IMPORTANT: Do not include specific years in your research queries unless explicitly requested by the user. When users ask for "recent" information, search for topics without date restrictions and let the search results determine recency.

You must ALWAYS respond in structured JSON format. You can either:
1. Provide content to the user (with control over whether conversation continues)
2. Call a tool to perform an action

Your response must be valid JSON matching this schema:
{
  "type": "content" | "tool_call",
  "content": "string (required if type is content)",
  "finished": "boolean (optional, for content only - defaults to true)",
  "tool_call": {
    "id": "unique_id",
    "name": "tool_name",
    "arguments": {...}
  } (required if type is tool_call)
}

COMMUNICATION FLEXIBILITY: You have full freedom to communicate throughout your work:
- You can provide updates, explanations, or context at any point
- Use "finished": false in content responses to continue the conversation after providing text
- Use "finished": true (or omit) in content responses when you're completely done
- You can mix content and tool calls freely: content -> tool -> content -> tool -> content (finished)

Available tools:
- read_board: View current board contents (no parameters) - Returns available element IDs for connections
- create_element: Add sticky notes, text, articles, or images (type, position, content). For images, provide the image URL in 'url' parameter and caption in 'content' - the system will automatically download and store the image.
- create_connection: Connect elements (from_element_id, to_element_id) - Use exact element IDs from read_board or newly created elements
- move_viewport: Adjust user's view (target_position, zoom_level)
- update_element: Modify elements (element_id, updates)
- delete_element: Remove elements (element_id)
- delete_connection: Remove connections (connection_id OR from_element_id + to_element_id)
- exa_search: Search web for multiple sources (search_term, num_results, search_options)
- exa_answer: Get direct answers to questions (question)
- exa_research: Conduct comprehensive research (research_topic)

ENHANCED CONNECTION CREATION:
- The system now efficiently tracks element IDs throughout the conversation
- When you create elements, their IDs are immediately available for connections
- You can create multiple elements and connect them without needing to read the board again
- Always use the exact element IDs returned from create_element or shown in read_board results
- The system validates element existence before creating connections

Rules:
1. Be proactive - automatically add research findings to the board
2. Use tools to gather information before responding
3. Provide helpful, engaging responses
4. Always use proper JSON format
5. Generate unique IDs for tool calls using timestamp + random
6. Do not include specific years in search queries unless explicitly requested
7. Don't bother mentioning the id of elements or connections. Just use the name or a summary of the content.
8. Communicate freely throughout your work - provide updates, explanations, and context as needed
9. When creating connections, use the exact element IDs from previous tool results or board reads

Examples:
Continuing content: {"type": "content", "content": "I'll help you research that topic. Let me start by checking your current board.", "finished": false}
Tool call: {"type": "tool_call", "tool_call": {"id": "read_board_123", "name": "read_board", "arguments": {}}}
Final content: {"type": "content", "content": "I've completed all the requested actions successfully!", "finished": true}
Ending content (default): {"type": "content", "content": "Here's your research summary with new board elements added."}`;

// Enhanced board state cache for efficient ID tracking
interface BoardStateCache {
  elements: Map<string, any>; // Map of element ID to element data
  connections: Map<string, any>; // Map of connection ID to connection data
  elementsByContent: Map<string, string>; // Map of content hash to element ID for deduplication
}

// Helper function to construct proper base URL for server-side requests
function getBaseUrl(): string {
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  return 'http://localhost:3000';
}

// Tool calling helper function with enhanced ID tracking
async function callTool(
  toolCall: ToolCall,
  request: NextRequest,
  boardId?: string,
  boardData?: any,
  boardStateCache?: BoardStateCache
): Promise<ToolResult> {
  try {
    // Only add board_id to parameters for tools that actually need it
    const boardRelatedTools = ['read_board', 'create_element', 'create_connection', 'move_viewport', 'update_element', 'delete_element', 'delete_connection'];
    const updatedParameters = { ...toolCall.arguments };
    if (boardId && boardRelatedTools.includes(toolCall.name)) {
      updatedParameters.board_id = boardId;
    }

    const requestBody: any = {
      tool_name: toolCall.name,
      parameters: updatedParameters,
      board_id: boardId
    };

    // For read_board, we need to get fresh data, not the stale conversation snapshot
    if (toolCall.name === 'read_board') {
      console.log(`[AI Route callTool] read_board called - will fetch fresh data from database instead of using stale snapshot`);
      // Don't pass board_data for read_board - let it fetch fresh data from database
      // This ensures we see manually added elements, not just the conversation snapshot
    } else {
      // For other tools, pass the board data if available
      if (boardData) {
        requestBody.board_data = boardData;
      }
    }

    // Enhanced connection validation using cached IDs
    if (toolCall.name === 'create_connection' && boardStateCache) {
      const fromId = updatedParameters.from_element_id;
      const toId = updatedParameters.to_element_id;

      console.log(`[AI Route] Validating connection: ${fromId} -> ${toId}`);
      console.log(`[AI Route] Available element IDs in cache: ${Array.from(boardStateCache.elements.keys()).join(', ')}`);

      // Check if both elements exist in our cache
      if (!boardStateCache.elements.has(fromId)) {
        console.error(`[AI Route] Source element ${fromId} not found in cache`);
        return {
          success: false,
          error: `Source element with ID ${fromId} not found. Please ensure the element exists before creating a connection.`
        };
      }

      if (!boardStateCache.elements.has(toId)) {
        console.error(`[AI Route] Target element ${toId} not found in cache`);
        return {
          success: false,
          error: `Target element with ID ${toId} not found. Please ensure the element exists before creating a connection.`
        };
      }

      console.log(`[AI Route] Connection validation passed for ${fromId} -> ${toId}`);
    }

    const response = await fetch(`${getBaseUrl()}/api/ai/tools`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('cookie') || '',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Tool ${toolCall.name} failed`);
    }

    const result = await response.json();

    // Enhanced board data and cache updates
    if (toolCall.name === 'create_element' && result.success && result.data?.element) {
      const newElement = result.data.element;
      console.log(`[AI Route] Updating in-memory board data with new element: ${newElement.id} (${newElement.type})`);

      // Update board data
      if (boardData) {
        boardData.elements = boardData.elements || [];
        boardData.elements.push({
          id: newElement.id,
          type: newElement.type,
          content: newElement.content,
          title: newElement.title,
          url: newElement.url,
          position: newElement.position
        });
        console.log(`[AI Route] Board data now has ${boardData.elements.length} elements`);
      }

      // Update cache for immediate ID tracking
      if (boardStateCache) {
        boardStateCache.elements.set(newElement.id, newElement);

        // Create content hash for deduplication
        const contentHash = `${newElement.type}:${newElement.content}:${newElement.position.x}:${newElement.position.y}`;
        boardStateCache.elementsByContent.set(contentHash, newElement.id);

        console.log(`[AI Route] Added element ${newElement.id} to cache. Cache now has ${boardStateCache.elements.size} elements`);
      }
    } else if (toolCall.name === 'create_connection' && result.success && result.data?.connection) {
      const newConnection = result.data.connection;
      console.log(`[AI Route] Updating in-memory board data with new connection: ${newConnection.fromId} -> ${newConnection.toId}`);

      // Update board data
      if (boardData) {
        boardData.connections = boardData.connections || [];
        boardData.connections.push({
          id: newConnection.id,
          fromId: newConnection.fromId,
          toId: newConnection.toId,
          label: newConnection.label
        });
        console.log(`[AI Route] Board data now has ${boardData.connections.length} connections`);
      }

      // Update cache
      if (boardStateCache) {
        boardStateCache.connections.set(newConnection.id, newConnection);
        console.log(`[AI Route] Added connection ${newConnection.id} to cache. Cache now has ${boardStateCache.connections.size} connections`);
      }
    } else if (toolCall.name === 'update_element' && result.success && result.data?.operation) {
      const operation = result.data.operation;
      console.log(`[AI Route] Processing update_element operation:`, operation);

      // Update board data
      if (boardData && operation.elementId) {
        const elementIndex = boardData.elements.findIndex((el: any) => el.id === operation.elementId);
        if (elementIndex !== -1) {
          boardData.elements[elementIndex] = { ...boardData.elements[elementIndex], ...operation.updates };
          console.log(`[AI Route] Updated element ${operation.elementId} in board data`);
        }
      }

      // Update cache
      if (boardStateCache && operation.elementId) {
        const cachedElement = boardStateCache.elements.get(operation.elementId);
        if (cachedElement) {
          const updatedElement = { ...cachedElement, ...operation.updates };
          boardStateCache.elements.set(operation.elementId, updatedElement);
          console.log(`[AI Route] Updated element ${operation.elementId} in cache`);
        }
      }
    } else if (toolCall.name === 'delete_element' && result.success && result.data?.operation) {
      const operation = result.data.operation;
      console.log(`[AI Route] Processing delete_element operation:`, operation);

      // Update board data
      if (boardData && operation.elementId) {
        boardData.elements = boardData.elements.filter((el: any) => el.id !== operation.elementId);
        // Also remove any connections to/from this element
        boardData.connections = boardData.connections.filter((conn: any) =>
          conn.fromId !== operation.elementId && conn.toId !== operation.elementId
        );
        console.log(`[AI Route] Removed element ${operation.elementId} from board data`);
      }

      // Update cache
      if (boardStateCache && operation.elementId) {
        boardStateCache.elements.delete(operation.elementId);
        // Remove connections involving this element
        for (const [connId, conn] of boardStateCache.connections.entries()) {
          if (conn.fromId === operation.elementId || conn.toId === operation.elementId) {
            boardStateCache.connections.delete(connId);
          }
        }
        console.log(`[AI Route] Removed element ${operation.elementId} from cache`);
      }
    } else if (toolCall.name === 'delete_connection' && result.success && result.data?.operation) {
      const operation = result.data.operation;
      console.log(`[AI Route] Processing delete_connection operation:`, operation);

      // Update board data
      if (boardData) {
        if (operation.connectionId) {
          // Delete by connection ID
          boardData.connections = boardData.connections.filter((conn: any) => conn.id !== operation.connectionId);
          console.log(`[AI Route] Removed connection ${operation.connectionId} from board data`);
        } else if (operation.fromElementId && operation.toElementId) {
          // Delete by element IDs
          boardData.connections = boardData.connections.filter((conn: any) =>
            !(conn.fromId === operation.fromElementId && conn.toId === operation.toElementId)
          );
          console.log(`[AI Route] Removed connection ${operation.fromElementId} -> ${operation.toElementId} from board data`);
        }
      }

      // Update cache
      if (boardStateCache) {
        if (operation.connectionId) {
          boardStateCache.connections.delete(operation.connectionId);
          console.log(`[AI Route] Removed connection ${operation.connectionId} from cache`);
        } else if (operation.fromElementId && operation.toElementId) {
          // Find and remove connection by element IDs
          for (const [connId, conn] of boardStateCache.connections.entries()) {
            if (conn.fromId === operation.fromElementId && conn.toId === operation.toElementId) {
              boardStateCache.connections.delete(connId);
              console.log(`[AI Route] Removed connection ${connId} (${operation.fromElementId} -> ${operation.toElementId}) from cache`);
              break;
            }
          }
        }
      }
    }

    return result;
  } catch (error) {
    console.error(`Error calling tool ${toolCall.name}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Tool execution failed'
    };
  }
}

// New structured message processing with enhanced board state caching
async function processStructuredConversation(
  messages: AIMessage[],
  model: string,
  request: NextRequest,
  boardId?: string,
  boardData?: any
): Promise<Response> {
  // Create a mutable reference to board data that persists throughout the conversation
  // CRITICAL FIX: Always use the fresh board data passed to this conversation
  // The client sends fresh board data with each message, so we should use it directly
  const mutableBoardData = boardData ? { ...boardData } : null;
  console.log(`[AI Route] Starting conversation with ${mutableBoardData?.elements?.length || 0} elements in board data`);
  console.log(`[AI Route] Board data elements:`, mutableBoardData?.elements?.map((e: { id: string }) => e.id) || []);

  // Initialize enhanced board state cache for efficient ID tracking
  const boardStateCache: BoardStateCache = {
    elements: new Map(),
    connections: new Map(),
    elementsByContent: new Map()
  };

  // Populate cache with existing board data
  if (mutableBoardData) {
    // Cache existing elements
    if (mutableBoardData.elements) {
      mutableBoardData.elements.forEach((element: any) => {
        boardStateCache.elements.set(element.id, element);

        // Create content hash for deduplication
        const contentHash = `${element.type}:${element.content}:${element.position.x}:${element.position.y}`;
        boardStateCache.elementsByContent.set(contentHash, element.id);
      });
      console.log(`[AI Route] Cached ${boardStateCache.elements.size} existing elements`);
    }

    // Cache existing connections
    if (mutableBoardData.connections) {
      mutableBoardData.connections.forEach((connection: any) => {
        boardStateCache.connections.set(connection.id, connection);
      });
      console.log(`[AI Route] Cached ${boardStateCache.connections.size} existing connections`);
    }
  }

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      let streamClosed = false;
      
      // Helper to send structured chunks (with safety check)
      const sendChunk = (data: any) => {
        if (streamClosed) {
          console.log('[AI_STREAM] Attempted to send chunk to closed stream:', data);
          return;
        }
        try {
          const chunk = JSON.stringify(data);
          controller.enqueue(encoder.encode(`data: ${chunk}\n\n`));
        } catch (error: any) {
          if (error.code === 'ERR_INVALID_STATE') {
          } else {
            throw error;
          }
        }
      };
      
      // Helper to send tool status updates
      const sendToolUpdate = (toolCall: ToolCall, status: 'executing' | 'completed' | 'failed', result?: any, error?: string) => {
        console.log('[AI /api/ai/route.ts] Sending tool update:', toolCall, status, result, error);
        sendChunk({
          type: 'tool_update',
          tool_call_id: toolCall.id,
          tool_name: toolCall.name,
          status,
          result,
          error
        });
      };

      // Helper to send content updates (for streaming text)
      const sendContentUpdate = (content: string, finished: boolean = false, isIncremental: boolean = true) => {
        sendChunk({
          type: 'content',
          content,
          finished,
          incremental: isIncremental
        });
      };

      // Helper to send thinking status
      const sendThinkingUpdate = (isThinking: boolean) => {
        sendChunk({
          type: 'thinking',
          thinking: isThinking
        });
      };

      try {
        const apiKey = process.env.OPEN_ROUTER_API_KEY;
        if (!apiKey) {
          throw new Error('OpenRouter API key is not configured');
        }

        let conversationMessages = [...messages];
        let hasMoreTools = true;
        let toolCallCount = 0;
        const maxToolCalls = AI_CONFIG.MAX_TOOL_CALLS;
        let completionSent = false;

        // Track streaming state
        // Note: Content accumulation is now handled incrementally in the frontend

        while (hasMoreTools && toolCallCount < maxToolCalls) {
          // Request structured JSON response with streaming enabled
          const payload = {
            model: model === 'standard' ? 'moonshotai/kimi-k2' : model,
            messages: [
              { role: 'system', content: SYSTEM_PROMPT },
              ...conversationMessages.map(msg => ({ 
                role: msg.role, 
                content: 'content' in msg ? msg.content : '' 
              }))
            ],
            response_format: {
              type: 'json_schema',
              json_schema: {
                name: 'ai_response',
                schema: AI_RESPONSE_SCHEMA
              }
            },
            stream: true  // Enable streaming
          };

          const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${apiKey}`,
              'HTTP-Referer': process.env.YOUR_SITE_URL || process.env.VERCEL_URL || 'http://localhost:3000',
              'X-Title': process.env.YOUR_SITE_NAME || 'Detective Board'
            },
            body: JSON.stringify(payload)
          });

          if (!response.ok) {
            throw new Error(`AI API request failed with status ${response.status}`);
          }

          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error('No readable stream available');
          }

          const decoder = new TextDecoder();
          let buffer = '';
          let currentlyStreaming = false;
          // Removed streamedContent as it's not used in this implementation
          let currentToolCall: ToolCall | null = null;
          let jsonBuffer = '';
          let insideJson = false;
          let braceCount = 0;
          let lastSentLength = 0;
          let isThinking = false;
          // Track string state across deltas for proper JSON parsing
          let inString = false;
          let escapeNext = false;
          
          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;
              
              buffer += decoder.decode(value, { stream: true });
              const lines = buffer.split('\n');
              buffer = lines.pop() || '';
              
              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  const data = line.slice(6);
                  if (data === '[DONE]') {
                    break;
                  }
                  
                  try {
                    const parsed = JSON.parse(data);
                    const delta = parsed.choices?.[0]?.delta?.content;

                    if (delta) {
                      // Check if this delta contains reserved tokens (model thinking)
                      const hasReservedTokens = /<\|reserved_token_\d+\|>/.test(delta) ||
                                               /<\|[^|]*\|>/.test(delta) ||
                                               /\uFFFD/.test(delta);

                      // Send thinking signal if we detect reserved tokens and haven't started content yet
                      if (hasReservedTokens && !currentlyStreaming && !isThinking) {
                        isThinking = true;
                        sendThinkingUpdate(true);
                      }

                      // Filter out reserved tokens and other special tokens from AI models
                      const filteredDelta = delta
                        .replace(/<\|reserved_token_\d+\|>/g, '') // Moonshot AI reserved tokens
                        .replace(/<\|[^|]*\|>/g, '') // Other special tokens in <|token|> format
                        .replace(/\uFFFD/g, ''); // Unicode replacement character

                      // Only log and process if there's actual content after filtering
                      if (filteredDelta) {
                        // Stop thinking mode when we get actual content
                        if (isThinking) {
                          isThinking = false;
                          sendThinkingUpdate(false);
                        }
                        jsonBuffer += filteredDelta;

                        // Track JSON structure using filtered delta, ignoring braces inside strings
                        // Use persistent string state variables to track across deltas
                        for (let i = 0; i < filteredDelta.length; i++) {
                          const char = filteredDelta[i];

                          if (escapeNext) {
                            escapeNext = false;
                            continue;
                          }

                          if (char === '\\' && inString) {
                            escapeNext = true;
                            continue;
                          }

                          if (char === '"') {
                            inString = !inString;
                            continue;
                          }

                          // Only count braces when not inside a string
                          if (!inString) {
                            if (char === '{') {
                              braceCount++;
                              if (braceCount === 1) {
                                insideJson = true;
                                console.log('[AI_STREAM] Starting JSON object detection');
                              }
                            } else if (char === '}') {
                              braceCount--;
                              if (braceCount === 0) {
                                console.log('[AI_STREAM] JSON object completed, braceCount=0');
                              }
                            }
                          }
                        }

                      // Try to detect and stream content incrementally
                      if (insideJson) {
                        // Look for content type and extract content progressively
                        const typeMatch = jsonBuffer.match(/"type"\s*:\s*"content"/);
                        if (typeMatch && !currentlyStreaming) {
                          console.log('[AI_STREAM] Starting content streaming detection');
                          currentlyStreaming = true;
                        }

                        if (currentlyStreaming) {
                          // Extract content using a more robust approach that streams character by character
                          const contentStartMatch = jsonBuffer.match(/"content"\s*:\s*"/);
                          if (contentStartMatch) {
                            const contentStart = contentStartMatch.index! + contentStartMatch[0].length;
                            let currentPos = contentStart;
                            let escapeNext = false;
                            let extractedContent = '';

                            // Extract content character by character, handling escapes
                            while (currentPos < jsonBuffer.length) {
                              const char = jsonBuffer[currentPos];

                              if (escapeNext) {
                                // Handle escaped characters
                                if (char === 'n') {
                                  extractedContent += '\n';
                                } else if (char === 't') {
                                  extractedContent += '\t';
                                } else if (char === '\\') {
                                  extractedContent += '\\';
                                } else if (char === '"') {
                                  extractedContent += '"';
                                } else {
                                  extractedContent += char;
                                }
                                escapeNext = false;
                              } else if (char === '\\') {
                                escapeNext = true;
                              } else if (char === '"') {
                                // End of content string
                                break;
                              } else {
                                extractedContent += char;
                              }
                              currentPos++;
                            }

                            // Send new characters as they arrive
                            if (extractedContent.length > lastSentLength) {
                              const newChunk = extractedContent.slice(lastSentLength);
                              if (newChunk.length > 0) {
                                sendContentUpdate(newChunk, false, true);
                                lastSentLength = extractedContent.length;
                              }
                            }
                          }
                        }
                        }
                      }

                      // Look for tool call being initiated
                          const toolCallMatch = jsonBuffer.match(/"type"\s*:\s*"tool_call".*?"name"\s*:\s*"([^"]*)".*?"id"\s*:\s*"([^"]*)"/) ||
                                               jsonBuffer.match(/"type"\s*:\s*"tool_call".*?"id"\s*:\s*"([^"]*)".*?"name"\s*:\s*"([^"]*)"/);
                          if (toolCallMatch && !currentToolCall) {
                            const [, nameOrId, idOrName] = toolCallMatch;
                            const toolName = jsonBuffer.includes('"name"') && jsonBuffer.indexOf('"name"') < jsonBuffer.indexOf('"id"') ? nameOrId : idOrName;
                            const toolId = jsonBuffer.includes('"name"') && jsonBuffer.indexOf('"name"') < jsonBuffer.indexOf('"id"') ? idOrName : nameOrId;
                            
                            currentToolCall = {
                              id: toolId,
                              name: toolName,
                              arguments: {}
                            };
                            
                            // Send immediate tool start notification
                            sendToolUpdate(currentToolCall, 'executing');
                          }

                      // Check if we have a complete JSON object
                      // Only try to parse when braceCount is 0, we're inside JSON, and we're not in the middle of a string
                      if (braceCount === 0 && insideJson && !inString) {
                        console.log('[AI_STREAM] Complete JSON detected, attempting to parse:', jsonBuffer.substring(0, 200) + '...');
                        try {
                          const structuredResponse: StructuredAIResponse = JSON.parse(jsonBuffer);
                          console.log('[AI /api/ai/route.ts] Parsed structuredResponse:', JSON.stringify(structuredResponse, null, 2));

                          // Reset for next JSON object - ONLY after successful parsing
                          jsonBuffer = '';
                          insideJson = false;
                          currentlyStreaming = false;
                          lastSentLength = 0;
                          currentToolCall = null;
                          // Reset string tracking state
                          inString = false;
                          escapeNext = false;

                          if (structuredResponse.type === 'content') {
                            // Only send completion signal when JSON is complete, don't send content again
                            // (content was already streamed incrementally above)
                            const finished = structuredResponse.finished !== false;
                            if (finished) {
                              sendContentUpdate('', true, false); // Send completion signal only
                              hasMoreTools = false;
                              break;
                            } else {
                              if (structuredResponse.content) {
                                conversationMessages.push({
                                  role: 'assistant',
                                  content: structuredResponse.content
                                });
                              }
                              break;
                            }
                          } else if (structuredResponse.type === 'tool_call' && structuredResponse.tool_call) {
                            // Log the tool call arguments before calling the tool
                            console.log('[AI /api/ai/route.ts] Tool call:', structuredResponse.tool_call);
                            // Complete the tool call
                            const toolCall: ToolCall = structuredResponse.tool_call;
                            toolCallCount++;

                            // If we haven't sent the executing status yet, send it now
                            if (!currentToolCall || (currentToolCall as ToolCall).id !== toolCall.id) {
                              sendToolUpdate(toolCall, 'executing');
                            }

                            // Execute the tool with mutable board data and enhanced cache
                            const toolResult = await callTool(toolCall, request, boardId, mutableBoardData, boardStateCache);
                            console.log('[AI /api/ai/route.ts] Tool result:', toolResult);

                            if (toolResult.success) {
                              sendToolUpdate(toolCall, 'completed', toolResult.data);
                            } else {
                              sendToolUpdate(toolCall, 'failed', undefined, toolResult.error);

                              // Continue with error info
                              conversationMessages.push({
                                role: 'assistant',
                                content: `Tool call failed: ${toolResult.error}`
                              });
                              break;
                            }

                            // Add tool call and result to conversation
                            conversationMessages.push({
                              role: 'assistant',
                              tool_call: toolCall
                            } as any);

                            conversationMessages.push({
                              role: 'tool',
                              tool_call_id: toolCall.id,
                              name: toolCall.name,
                              content: JSON.stringify(toolResult)
                            });

                            break;
                          }
                        } catch (parseError) {
                          // JSON not complete yet, continue streaming
                          console.log('[AI_STREAM] JSON parsing failed, continuing to accumulate:', parseError);
                        }
                      }
                    }
                  } catch (error) {
                    // Skip malformed chunks
                    console.warn('Skipped malformed stream chunk:', data);
                  }
                }
              }
            }
          } finally {
            try {
              reader.releaseLock();
            } catch (error) {
              // Stream may have already ended, ignore the error
              console.log('[AI_STREAM] Reader already released');
            }
          }
        }

        // Send completion signal (only if not already sent)
        if (!completionSent) {
          completionSent = true;
          sendChunk({ type: 'done' });
        }
        
      } catch (error) {
        console.error('Error in structured conversation:', error);
        sendChunk({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      } finally {
        streamClosed = true;
        try {
          controller.close();
        } catch (error: any) {
          if (error.code !== 'ERR_INVALID_STATE') {
            console.error('Error closing stream controller:', error);
          }
        }
      }
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    }
  });
}

export async function POST(request: NextRequest) {
  try {
    const { messages, model, response_format, stream, boardId, boardData } = await request.json();

    // CRITICAL DEBUG: Log what board data we receive from the client
    console.log(`[AI Route POST] Received request with boardId: ${boardId}`);
    console.log(`[AI Route POST] Board data received:`, boardData ? `${boardData.elements?.length || 0} elements, ${boardData.connections?.length || 0} connections` : 'null');
    if (boardData?.elements) {
      console.log(`[AI Route POST] Element IDs received:`, boardData.elements.map((e: any) => e.id));
    }

    if (!messages || !model) {
      return NextResponse.json({ error: 'Missing messages or model' }, { status: 400 });
    }

    // Convert messages to AIMessage format
    const aiMessages: AIMessage[] = messages.map((msg: any) => ({
      role: msg.role,
      content: msg.content
    }));

    // For non-streaming or JSON schema requests, use basic completion
    if (stream === false || response_format?.type === 'json_schema') {
      const payload: any = {
        model: model === 'standard' ? 'moonshotai/kimi-k2' : model,
        messages: [
          { role: 'system', content: SYSTEM_PROMPT },
          ...aiMessages
        ],
        stream: true,
      };

      if (response_format && response_format.type === 'json_schema') {
        payload.response_format = response_format;
      }

      const apiKey = process.env.OPEN_ROUTER_API_KEY;
      if (!apiKey) {
        return NextResponse.json({ error: 'OpenRouter API key is not configured' }, { status: 500 });
      }

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': process.env.YOUR_SITE_URL || process.env.VERCEL_URL || 'http://localhost:3000',
          'X-Title': process.env.YOUR_SITE_NAME || 'Detective Board'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to parse OpenRouter error response' }));
        console.error('OpenRouter API error:', response.status, errorData);
        return NextResponse.json({ error: errorData.error || 'Failed to get response from AI service' }, { status: response.status });
      }

      return NextResponse.json(await response.json());
    }

    // Use new structured conversation processing for streaming
    return await processStructuredConversation(aiMessages, model, request, boardId, boardData);

  } catch (error: any) {
    console.error('[API /ai POST] Error:', error);
    let errorMessage = 'Failed to process AI request';
    let statusCode = 500;
    
    if (error instanceof SyntaxError) {
      errorMessage = 'Invalid request format';
      statusCode = 400;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
} 