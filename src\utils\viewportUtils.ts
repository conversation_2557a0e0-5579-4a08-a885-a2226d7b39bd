import { Position } from '../types';

export interface ViewportElement {
  position: Position;
  width?: number;
  height?: number;
  type?: string;
}

export interface ViewportFocusOptions {
  /** Delay before applying viewport changes (default: 100ms) */
  delay?: number;
  /** Target zoom scale (default: auto-calculated) */
  targetScale?: number;
  /** Minimum zoom scale (default: 1.0) */
  minScale?: number;
  /** Maximum zoom scale (default: 2.0) */
  maxScale?: number;
  /** Padding around focused elements (default: 100px) */
  padding?: number;
  /** Whether to animate the transition (default: true) */
  animate?: boolean;
}

export interface ViewportBounds {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
  centerX: number;
  centerY: number;
  width: number;
  height: number;
}

/**
 * Calculate the bounding box for a set of elements
 */
export function calculateElementsBounds(elements: ViewportElement[]): ViewportBounds | null {
  if (elements.length === 0) return null;

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  elements.forEach(element => {
    const { position, width = 200, height = 100 } = element;
    const elementMinX = position.x;
    const elementMinY = position.y;
    const elementMaxX = position.x + width;
    const elementMaxY = position.y + height;

    minX = Math.min(minX, elementMinX);
    minY = Math.min(minY, elementMinY);
    maxX = Math.max(maxX, elementMaxX);
    maxY = Math.max(maxY, elementMaxY);
  });

  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;
  const width = maxX - minX;
  const height = maxY - minY;

  return {
    minX,
    minY,
    maxX,
    maxY,
    centerX,
    centerY,
    width,
    height
  };
}

/**
 * Calculate optimal viewport position and scale for focusing on elements
 */
export function calculateViewportFocus(
  elements: ViewportElement[],
  containerRef: React.RefObject<HTMLElement>,
  currentScale: number,
  options: ViewportFocusOptions = {}
): { position: Position; scale: number } | null {
  console.log(`[calculateViewportFocus] ===== CALCULATING VIEWPORT FOCUS =====`);
  console.log(`[calculateViewportFocus] Elements:`, elements);
  console.log(`[calculateViewportFocus] Current scale:`, currentScale);
  console.log(`[calculateViewportFocus] Options:`, options);

  const {
    targetScale,
    minScale = 1.0,
    maxScale = 2.0,
    padding = 100
  } = options;

  const bounds = calculateElementsBounds(elements);
  console.log(`[calculateViewportFocus] Calculated bounds:`, bounds);

  if (!bounds || !containerRef.current) {
    console.warn(`[calculateViewportFocus] No bounds or container ref. Bounds:`, bounds, `Container:`, containerRef.current);
    return null;
  }

  const containerRect = containerRef.current.getBoundingClientRect();
  const viewportWidth = containerRect.width;
  const viewportHeight = containerRect.height;

  let finalScale: number;

  if (elements.length === 1) {
    // Single element: use intelligent zoom level
    finalScale = targetScale || (currentScale < 0.8 ? 1.2 : Math.max(minScale, Math.min(maxScale, currentScale * 1.2)));
  } else {
    // Multiple elements: calculate scale to fit all elements
    const contentWidth = bounds.width + padding * 2;
    const contentHeight = bounds.height + padding * 2;
    
    const scaleX = viewportWidth / contentWidth;
    const scaleY = viewportHeight / contentHeight;
    const calculatedScale = Math.min(scaleX, scaleY);
    
    finalScale = targetScale || Math.max(minScale, Math.min(maxScale, calculatedScale));
  }

  // Calculate position to center the elements
  const viewportCenterX = viewportWidth / 2;
  const viewportCenterY = viewportHeight / 2;

  const targetPosition = {
    x: viewportCenterX - (bounds.centerX * finalScale),
    y: viewportCenterY - (bounds.centerY * finalScale)
  };

  console.log(`[calculateViewportFocus] Final result - Position:`, targetPosition, `Scale:`, finalScale);
  console.log(`[calculateViewportFocus] ===== VIEWPORT FOCUS CALCULATION COMPLETE =====`);

  return {
    position: targetPosition,
    scale: finalScale
  };
}

/**
 * Create a viewport focusing function with consistent behavior
 */
export function createViewportFocuser(
  containerRef: React.RefObject<HTMLElement>,
  setScaleAndPosition: (scale: number, position: Position) => void,
  getCurrentScale: () => number
) {
  return function focusOnElements(
    elements: ViewportElement[],
    options: ViewportFocusOptions = {}
  ): Promise<void> {
    return new Promise((resolve) => {
      const { delay = 100 } = options;

      setTimeout(() => {
        try {
          const currentScale = getCurrentScale();
          const focusResult = calculateViewportFocus(elements, containerRef, currentScale, options);
          
          if (focusResult) {
            console.log(`[Viewport Focus] Focusing on ${elements.length} elements with scale ${focusResult.scale}`);
            setScaleAndPosition(focusResult.scale, focusResult.position);
          }
          
          resolve();
        } catch (error) {
          console.error('[Viewport Focus] Error focusing on elements:', error);
          resolve();
        }
      }, delay);
    });
  };
}

/**
 * Visual feedback utilities for operations
 */
export interface VisualFeedbackOptions {
  /** Duration of the feedback effect (default: 3000ms) */
  duration?: number;
  /** Type of feedback effect */
  type?: 'highlight' | 'pulse' | 'glow';
  /** Color for the feedback effect */
  color?: string;
}

/**
 * Apply visual feedback to elements
 */
export function applyVisualFeedback(
  elementIds: string[],
  selectItem: (id: string | null) => void,
  setSelectedItemIds: (ids: string[]) => void,
  options: VisualFeedbackOptions = {}
): void {
  const { duration = 3000, type = 'highlight' } = options;

  if (elementIds.length === 0) return;

  // Apply highlighting after a short delay for better visual flow
  setTimeout(() => {
    if (elementIds.length > 1) {
      // Multiple elements: use multi-selection
      setSelectedItemIds(elementIds);
      // Clear the highlight after duration
      setTimeout(() => {
        setSelectedItemIds([]);
      }, duration);
    } else {
      // Single element: use regular selection
      selectItem(elementIds[0]);
      // Clear the highlight after duration
      setTimeout(() => {
        selectItem(null);
      }, duration);
    }
  }, 200); // Apply highlighting after viewport adjustment completes
}

/**
 * Animation presets for different operation types
 */
export const VIEWPORT_ANIMATION_PRESETS = {
  creation: {
    delay: 100,
    minScale: 1.0,
    maxScale: 2.0,
    padding: 100
  },
  connection: {
    delay: 150,
    minScale: 0.8,
    maxScale: 1.5,
    padding: 150
  },
  update: {
    delay: 50,
    minScale: 1.0,
    maxScale: 2.0,
    padding: 100
  },
  deletion: {
    delay: 200,
    minScale: 0.8,
    maxScale: 1.5,
    padding: 200
  }
} as const;
