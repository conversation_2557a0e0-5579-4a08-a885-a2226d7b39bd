import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import type { Database } from '@/lib/database.types'; // Assuming this path is correct

interface UserProfile {
    id: string;
    username: string;
    email: string;
    is_verified: boolean;
    created_at?: string;
    updated_at: string;
}

export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  let requestBody;
  try {
    requestBody = await request.json();
  } catch (error) {
    console.error('[API Complete Profile] Invalid JSON in request body:', error);
    return NextResponse.json({ error: 'Invalid request body. JSON expected.' }, { status: 400 });
  }

  const { username } = requestBody;

  if (!username || typeof username !== 'string' || username.trim().length < 3) {
    return NextResponse.json({ error: 'Username is required and must be at least 3 characters.' }, { status: 400 });
  }
  if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    return NextResponse.json({ error: 'Username can only contain letters, numbers, underscores, and hyphens.' }, { status: 400 });
  }

  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('[API Complete Profile] Auth error:', authError);
      return NextResponse.json({ error: 'User not authenticated or session expired.' }, { status: 401 });
    }

    if (!user.email) {
      console.error('[API Complete Profile] User email is missing in session.');
      return NextResponse.json({ error: 'User email is not available from session. Cannot complete profile.' }, { status: 400 });
    }

    const userId = user.id;
    const userEmail = user.email;

    // Check if username is already taken by someone else
    const { data: existingUsernameUser, error: usernameCheckError } = await supabase
      .from('users')
      .select('id')
      .eq('username', username)
      .neq('id', userId) // Important: Exclude the current user from this check
      .limit(1)
      .maybeSingle();

    if (usernameCheckError) {
        console.error('[API Complete Profile] Error checking username existence:', usernameCheckError);
        return NextResponse.json({ error: 'Server error checking username.' }, { status: 500 });
    }
    if (existingUsernameUser) {
        return NextResponse.json({ error: `Username '${username}' is already taken.` }, { status: 409 }); // 409 Conflict
    }

    // Check if a profile for the current user ID already exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('users')
      .select('id, created_at')
      .eq('id', userId)
      .maybeSingle();

    if (fetchError) {
      console.error('[API Complete Profile] Error fetching existing profile:', fetchError);
      return NextResponse.json({ error: 'Server error fetching profile.' }, { status: 500 });
    }

    const profileDataToUpsert: UserProfile = {
      id: userId,
      username: username,
      email: userEmail,
      is_verified: true,
      updated_at: new Date().toISOString(),
    };

    if (!existingProfile) {
      profileDataToUpsert.created_at = new Date().toISOString();
    }

    const { data: upsertedProfile, error: upsertError } = await supabase
      .from('users')
      .upsert(profileDataToUpsert, { onConflict: 'id' })
      .select()
      .single(); // Expect one row back after upsert

    if (upsertError) {
      console.error('[API Complete Profile] Error upserting profile:', upsertError);
      // RLS errors usually have code 42501, but message is more user-friendly
      if (upsertError.message.includes('permission denied')) {
        return NextResponse.json({ error: 'Database permission error. Please contact support.' }, { status: 500 });
      }
      return NextResponse.json({ error: `Failed to update profile: ${upsertError.message}` }, { status: 500 });
    }

    if (!upsertedProfile) {
      console.error('[API Complete Profile] Upsert operation returned no data.');
      return NextResponse.json({ error: 'Profile update did not return expected data.' }, { status: 500 });
    }

    console.log('[API Complete Profile] Profile upserted successfully for user:', userId);
    return NextResponse.json({ success: true, profile: upsertedProfile }, { status: 200 });

  } catch (error: any) {
    console.error('[API Complete Profile] Unhandled exception:', error);
    return NextResponse.json({ error: 'An unexpected server error occurred.' }, { status: 500 });
  }
} 