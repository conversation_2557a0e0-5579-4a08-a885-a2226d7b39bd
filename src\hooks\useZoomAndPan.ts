import { useState, useCallback, useRef, useEffect } from 'react';
// Import PanInfo type from our wrapper - PanInfo is not used here, consider removing if not needed elsewhere
// import { PanInfo } from '../utils/MotionWrapper';
import { Position, UseZoomAndPanReturn } from '../types';
import { useDrag } from '../context/DragContext';

/**
 * Constants for zoom and pan limits
 */
export const MIN_SCALE = 0.1;
export const MAX_SCALE = 3;
const ZOOM_SPEED = 0.05; // For wheel zoom

// Board size constraints (in pixels, at scale 1.0)
const BOARD_WIDTH = 19000; // Width of the virtual board
const BOARD_HEIGHT = 9200; // Height of the virtual board
const BOARD_PADDING = 100; // Padding around the board edges (allows slight overflow)

/**
 * Calculates the centered position for the board
 */
const calculateCenteredPosition = (
  viewportWidth: number,
  viewportHeight: number,
  scale: number
): Position => {
  const x = (viewportWidth - (BOARD_WIDTH * scale)) / 2;
  const y = (viewportHeight - (BOARD_HEIGHT * scale)) / 2;
  return { x, y };
};

// Helper to get distance between two points
const getDistance = (p1: {clientX: number, clientY: number}, p2: {clientX: number, clientY: number}): number => {
  return Math.sqrt(Math.pow(p2.clientX - p1.clientX, 2) + Math.pow(p2.clientY - p1.clientY, 2));
};

// Helper to get midpoint between two points
const getMidpoint = (p1: {clientX: number, clientY: number}, p2: {clientX: number, clientY: number}): Position => {
  return {
    x: (p1.clientX + p2.clientX) / 2,
    y: (p1.clientY + p2.clientY) / 2,
  };
};

// Helper function to prevent default touch move behavior
const preventDefaultTouchMove = (e: TouchEvent) => {
  // Check if the event target is a toolbar button or other UI element that should not be interfered with
  const target = e.target;
  if (target && target instanceof Element && (
    target.closest('[data-nodrag="true"]') ||
    target.closest('.fixed') ||  // Toolbar is fixed positioned
    target.closest('button') ||  // Any button element
    target.closest('[role="button"]') ||
    target.closest('.toolbar') ||
    target.closest('[data-toolbar]')
  )) {
    return; // Don't interfere with UI interactions
  }

  e.preventDefault();
};

// Mobile detection utility
const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         (navigator.maxTouchPoints && navigator.maxTouchPoints > 1);
};



// Mobile-specific gesture thresholds
const MOBILE_GESTURE_THRESHOLDS = {
  MIN_PINCH_DISTANCE: 10, // Minimum distance change to register as pinch
  MIN_PAN_DISTANCE: 3,    // Minimum distance change to register as pan
  GESTURE_TIMEOUT: 100,   // Max time between gesture events (ms)
  SCALE_PRECISION: 0.005, // Minimum scale change to apply
};

/**
 * Custom hook for handling zoom and pan functionality
 */
export const useZoomAndPan = (
  initialScale: number = 1,
  initialPositionProp: Position = { x: 0, y: 0 } // Renamed to avoid conflict
): UseZoomAndPanReturn => {
  // Get drag context to check if any items are being dragged
  const { isAnyItemDragging } = useDrag();

  const viewportRef = useRef({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
    left: 0, // Will be set from canvas element
    top: 0   // Will be set from canvas element
  });

  const centeredPosition = calculateCenteredPosition(
    viewportRef.current.width,
    viewportRef.current.height,
    initialScale
  );

  const [scale, setScaleInternal] = useState<number>(initialScale);
  const [position, setPositionInternal] = useState<Position>(centeredPosition);
  const [isGrabbing, setIsGrabbing] = useState<boolean>(false);

  // Refs for manual panning and pinch state
  const isPanningOrPinchingRef = useRef(false); // True if any pointer interaction is active
  const panStartPointerRef = useRef({ x: 0, y: 0 }); // For single pointer pan
  const panStartPositionRef = useRef({ x: 0, y: 0 }); // For single pointer pan

  // Refs for pinch-to-zoom with improved mobile handling
  const activePointersRef = useRef<Map<number, PointerEvent>>(new Map());
  const initialPinchDistanceRef = useRef<number>(0);
  const initialScaleRef = useRef<number>(1); // Stores scale at the start of a pinch
  // Stores the pinch center relative to the board (not viewport)
  const pinchStartBoardMidpointRef = useRef<Position>({ x: 0, y: 0 });
  const lastPinchDistanceRef = useRef<number>(0); // Track last distance for smooth transitions

  // New refs for better touch handling
  const lastScaleRef = useRef<number>(initialScale);
  const lastPositionRef = useRef<Position>(centeredPosition);
  const transformTimeoutRef = useRef<number | null>(null);

  // Mobile-specific refs
  const isGestureActiveRef = useRef<boolean>(false);
  const gestureStartTimeRef = useRef<number>(0);



  const isInitializedRef = useRef(false);

  // Store current position in a ref to avoid recreating handlers
  const positionRef = useRef(position);
  
  // Keep the ref updated when position changes
  useEffect(() => {
    positionRef.current = position;
  }, [position]);

  const constrainPosition = useCallback((pos: Position, currentScale: number): Position => {
    const vpWidth = viewportRef.current.width;
    const vpHeight = viewportRef.current.height;

    if (!vpWidth || !vpHeight) return pos;

    const scaledBoardWidth = BOARD_WIDTH * currentScale;
    const scaledBoardHeight = BOARD_HEIGHT * currentScale;

    const minX = vpWidth - scaledBoardWidth - BOARD_PADDING * currentScale;
    const minY = vpHeight - scaledBoardHeight - BOARD_PADDING * currentScale;
    const maxX = BOARD_PADDING * currentScale;
    const maxY = BOARD_PADDING * currentScale;

    const constrained = {
      x: Math.min(maxX, Math.max(minX, pos.x)),
      y: Math.min(maxY, Math.max(minY, pos.y))
    };
    return constrained;
  }, []);

  // Apply transform with hardware acceleration optimization
  const applyTransform = useCallback((newScale: number, newPosition: Position) => {
    if (transformTimeoutRef.current) {
      cancelAnimationFrame(transformTimeoutRef.current);
      transformTimeoutRef.current = null; // Clear the ref
    }

    // Directly apply state updates
    setScaleInternal(newScale);
    setPositionInternal(newPosition);

    // Update refs immediately for consistent state during gestures
    lastScaleRef.current = newScale;
    lastPositionRef.current = newPosition;
    positionRef.current = newPosition; // Update position ref immediately

  }, []); // Keep empty deps as setScaleInternal and setPositionInternal are stable

  const setScale = useCallback((newScale: number, pinchMidpointClient?: Position) => {
    const constrainedScale = Math.min(Math.max(newScale, MIN_SCALE), MAX_SCALE);
    
    if (constrainedScale === scale) return;

    let newPosition;
    if (pinchMidpointClient) { 
      const boardX = (pinchMidpointClient.x - viewportRef.current.left - position.x) / scale;
      const boardY = (pinchMidpointClient.y - viewportRef.current.top - position.y) / scale;
      
      const newX = (pinchMidpointClient.x - viewportRef.current.left) - boardX * constrainedScale;
      const newY = (pinchMidpointClient.y - viewportRef.current.top) - boardY * constrainedScale;

      newPosition = constrainPosition({ x: newX, y: newY }, constrainedScale);
    } else { 
      // Zoom to the center of the viewport
      const viewportCenterX = (viewportRef.current.width / 2);
      const viewportCenterY = (viewportRef.current.height / 2);

      const boardX = (viewportCenterX - position.x) / scale;
      const boardY = (viewportCenterY - position.y) / scale;

      const newX = viewportCenterX - boardX * constrainedScale;
      const newY = viewportCenterY - boardY * constrainedScale;
      
      newPosition = constrainPosition({ x: newX, y: newY }, constrainedScale);
    }
    
    applyTransform(constrainedScale, newPosition);
  }, [scale, position, constrainPosition, viewportRef, applyTransform]);


  const setPosition = useCallback((newPosition: Position) => {
    const constrained = constrainPosition(newPosition, scale);
    applyTransform(scale, constrained);
  }, [scale, constrainPosition, applyTransform]);

  // New function to set both scale and position simultaneously
  const setScaleAndPosition = useCallback((newScale: number, newPosition: Position) => {
    const constrainedScale = Math.min(Math.max(newScale, MIN_SCALE), MAX_SCALE);
    const constrainedPosition = constrainPosition(newPosition, constrainedScale);
    applyTransform(constrainedScale, constrainedPosition);
  }, [constrainPosition, applyTransform]);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    
    if (e.currentTarget) {
      const rect = e.currentTarget.getBoundingClientRect();
      viewportRef.current = { 
        width: rect.width, 
        height: rect.height,
        left: rect.left,
        top: rect.top,
      };
    }
    
    const delta = e.deltaY;
    const newScaleAttempt = delta > 0 
      ? scale - ZOOM_SPEED * scale 
      : scale + ZOOM_SPEED * scale;
    
    const newScale = Math.min(Math.max(newScaleAttempt, MIN_SCALE), MAX_SCALE);
    
    if (newScale !== scale) {
      const rect = e.currentTarget.getBoundingClientRect();
      const cursorX = e.clientX - rect.left;
      const cursorY = e.clientY - rect.top;
      
      const boardX = (cursorX - position.x) / scale;
      const boardY = (cursorY - position.y) / scale;
      
      const newX = cursorX - boardX * newScale;
      const newY = cursorY - boardY * newScale;
      
      applyTransform(newScale, constrainPosition({ x: newX, y: newY }, newScale));
    }
  }, [scale, position, constrainPosition, applyTransform]);

  // Global pointer move handler with improved mobile support
  const handleCanvasPointerMove = useCallback((event: PointerEvent) => {
    if (!isPanningOrPinchingRef.current) {
      return;
    }

    // Check if the event target is a toolbar button or other UI element that should not be interfered with
    const target = event.target;
    if (target && target instanceof Element && (
      target.closest('[data-nodrag="true"]') ||
      target.closest('.fixed') ||  // Toolbar is fixed positioned
      target.closest('button') ||  // Any button element
      target.closest('[role="button"]') ||
      target.closest('.toolbar') ||
      target.closest('[data-toolbar]')
    )) {
      return; // Don't interfere with UI interactions
    }

    // Prevent default behavior for canvas touch events only
    event.preventDefault();

    // Update the pointer in our active map
    if (!activePointersRef.current.has(event.pointerId)) {
      return; // Pointer not found in active list
    }

    activePointersRef.current.set(event.pointerId, event);
    const activePointers = Array.from(activePointersRef.current.values());

    if (activePointers.length === 2) {
      // Check if any item is being dragged - if so, abort pinch-to-zoom
      if (isAnyItemDragging()) {
        // Reset pinch state and exit to prevent interference with drag
        initialPinchDistanceRef.current = 0;
        lastPinchDistanceRef.current = 0;
        isGestureActiveRef.current = false;
        return;
      }

      // Handle pinch-to-zoom with improved mobile support
      const [p1, p2] = activePointers;
      const currentDistance = getDistance(p1, p2);

      // Initialize pinch gesture if needed
      if (initialPinchDistanceRef.current === 0) {
        initialPinchDistanceRef.current = currentDistance;
        lastPinchDistanceRef.current = currentDistance;
        initialScaleRef.current = lastScaleRef.current;
        isGestureActiveRef.current = true;
        gestureStartTimeRef.current = Date.now();

        const clientMidpoint = getMidpoint(p1, p2);
        pinchStartBoardMidpointRef.current = {
          x: (clientMidpoint.x - viewportRef.current.left - positionRef.current.x) / lastScaleRef.current,
          y: (clientMidpoint.y - viewportRef.current.top - positionRef.current.y) / lastScaleRef.current,
        };
        return;
      }

      // Mobile-optimized distance tracking
      const isMobile = isMobileDevice();
      const minDistanceThreshold = isMobile ? MOBILE_GESTURE_THRESHOLDS.MIN_PINCH_DISTANCE : 2;
      const distanceDelta = Math.abs(currentDistance - lastPinchDistanceRef.current);

      if (distanceDelta < minDistanceThreshold) {
        return; // Ignore very small movements to reduce jitter
      }

      // Check gesture timing for mobile
      if (isMobile) {
        const now = Date.now();
        const timeSinceStart = now - gestureStartTimeRef.current;
        if (timeSinceStart > MOBILE_GESTURE_THRESHOLDS.GESTURE_TIMEOUT && distanceDelta < minDistanceThreshold * 2) {
          return; // Ignore delayed small movements on mobile
        }
      }

      lastPinchDistanceRef.current = currentDistance;

      // Calculate scale with mobile-optimized precision
      const scaleMultiplier = currentDistance / initialPinchDistanceRef.current;
      const newScaleValue = initialScaleRef.current * scaleMultiplier;
      const finalNewScale = Math.min(Math.max(newScaleValue, MIN_SCALE), MAX_SCALE);

      // Mobile-optimized scale change threshold
      const scaleThreshold = isMobile ? MOBILE_GESTURE_THRESHOLDS.SCALE_PRECISION : 0.001;
      if (Math.abs(finalNewScale - lastScaleRef.current) < scaleThreshold) {
        return;
      }

      // Calculate the current midpoint and adjust position to zoom around it
      const currentClientMidpoint = getMidpoint(p1, p2);
      const newPosX = (currentClientMidpoint.x - viewportRef.current.left) - (pinchStartBoardMidpointRef.current.x * finalNewScale);
      const newPosY = (currentClientMidpoint.y - viewportRef.current.top) - (pinchStartBoardMidpointRef.current.y * finalNewScale);

      applyTransform(finalNewScale, constrainPosition({ x: newPosX, y: newPosY }, finalNewScale));

    } else if (activePointers.length === 1 && !isGestureActiveRef.current) {
      // Handle single-finger panning (only if not in a pinch gesture)
      const pointer = activePointers[0];
      const dx = pointer.clientX - panStartPointerRef.current.x;
      const dy = pointer.clientY - panStartPointerRef.current.y;

      // Mobile-optimized movement threshold
      const isMobile = isMobileDevice();
      const minPanThreshold = isMobile ? MOBILE_GESTURE_THRESHOLDS.MIN_PAN_DISTANCE : 1;

      if (Math.abs(dx) < minPanThreshold && Math.abs(dy) < minPanThreshold) {
        return;
      }

      const newX = panStartPositionRef.current.x + dx;
      const newY = panStartPositionRef.current.y + dy;

      applyTransform(lastScaleRef.current, constrainPosition({ x: newX, y: newY }, lastScaleRef.current));
    }
  }, [constrainPosition, applyTransform, isAnyItemDragging]);

  // Global pointer up handler with improved mobile support
  const handleCanvasPointerUp = useCallback((event: PointerEvent) => {
    // Check if the event target is a toolbar button or other UI element that should not be interfered with
    const target = event.target;
    if (target && target instanceof Element && (
      target.closest('[data-nodrag="true"]') ||
      target.closest('.fixed') ||  // Toolbar is fixed positioned
      target.closest('button') ||  // Any button element
      target.closest('[role="button"]') ||
      target.closest('.toolbar') ||
      target.closest('[data-toolbar]')
    )) {
      return; // Don't interfere with UI interactions
    }

    try {
      if (event.target instanceof Element) {
        event.target.releasePointerCapture(event.pointerId);
      }
    } catch (e) {
      // Ignore errors from releasePointerCapture
    }

    const hadPointer = activePointersRef.current.has(event.pointerId);
    if (!hadPointer) {
      return; // Pointer wasn't being tracked
    }

    const initialLength = activePointersRef.current.size;
    activePointersRef.current.delete(event.pointerId);
    const finalLength = activePointersRef.current.size;

    // Reset pinch state when going from 2+ to <2 pointers
    if (finalLength < 2 && initialLength >= 2) {
      initialPinchDistanceRef.current = 0;
      lastPinchDistanceRef.current = 0;
      initialScaleRef.current = lastScaleRef.current;
      pinchStartBoardMidpointRef.current = {x: 0, y: 0};
      isGestureActiveRef.current = false;
      gestureStartTimeRef.current = 0;

      window.removeEventListener('touchmove', preventDefaultTouchMove);
    }

    if (finalLength === 1) {
      // Transition to single-finger panning
      const remainingPointer = Array.from(activePointersRef.current.values())[0];
      panStartPointerRef.current = { x: remainingPointer.clientX, y: remainingPointer.clientY };
      panStartPositionRef.current = { x: positionRef.current.x, y: positionRef.current.y };
      isPanningOrPinchingRef.current = true;
      setIsGrabbing(true);

      window.addEventListener('touchmove', preventDefaultTouchMove, { passive: false });
    } else if (finalLength === 0) {
      // Clean up all gesture state
      isPanningOrPinchingRef.current = false;
      setIsGrabbing(false);
      isGestureActiveRef.current = false;
      gestureStartTimeRef.current = 0;

      window.removeEventListener('pointermove', handleCanvasPointerMove);
      window.removeEventListener('pointerup', handleCanvasPointerUp);
      window.removeEventListener('pointercancel', handleCanvasPointerUp);
      window.removeEventListener('touchmove', preventDefaultTouchMove);
    }
  }, [positionRef, handleCanvasPointerMove, setIsGrabbing]);

  // Improved pointer down handler for the canvas element
  const handleCanvasElementPointerDown = useCallback((event: React.PointerEvent<Element>) => {
    // Always prevent default to stop browser behaviors FIRST
    event.preventDefault();
    event.stopPropagation();

    // Store the current target for consistent pointer capture management
    const currentTargetElement = event.currentTarget as Element;

    if (event.target instanceof Element && event.target.closest('.board-item-draggable')) {
      return;
    }
    if (event.pointerType === 'mouse' && event.button !== 0) {
      return;
    }

    if (event.currentTarget) {
      const rect = event.currentTarget.getBoundingClientRect();
      viewportRef.current = { 
        width: rect.width, 
        height: rect.height,
        left: rect.left,
        top: rect.top
      };
    }
    
    // Only capture the first pointer
    if (activePointersRef.current.size === 0) {
      try {
        if (event.target instanceof Element) {
          event.target.setPointerCapture(event.pointerId);
        }
      } catch (e) {
        console.error("Error capturing first pointer:", e);
      }
    }
    
    // Add pointer to our tracking map
    activePointersRef.current.set(event.pointerId, event.nativeEvent as PointerEvent);

    if (activePointersRef.current.size === 1) {
      // Start single-finger interaction
      isPanningOrPinchingRef.current = true;
      setIsGrabbing(true);
      panStartPointerRef.current = { x: event.clientX, y: event.clientY };
      panStartPositionRef.current = { x: positionRef.current.x, y: positionRef.current.y };

      // Reset pinch state for single finger
      initialPinchDistanceRef.current = 0;
      lastPinchDistanceRef.current = 0;
      initialScaleRef.current = lastScaleRef.current;
      pinchStartBoardMidpointRef.current = {x: 0, y: 0};
      isGestureActiveRef.current = false;

      // Add event listeners only once
      if (!isPanningOrPinchingRef.current || activePointersRef.current.size === 1) {
        window.addEventListener('pointermove', handleCanvasPointerMove);
        window.addEventListener('pointerup', handleCanvasPointerUp);
        window.addEventListener('pointercancel', handleCanvasPointerUp);
        window.addEventListener('touchmove', preventDefaultTouchMove, { passive: false });
      }

    } else if (activePointersRef.current.size === 2) {
      // Check if any item is being dragged - if so, ignore the second pointer to prevent conflicts
      if (isAnyItemDragging()) {
        // Remove the second pointer from tracking to prevent interference with drag
        activePointersRef.current.delete(event.pointerId);
        return;
      }

      // Initialize pinch-to-zoom only if no items are being dragged
      const activePointers = Array.from(activePointersRef.current.values());
      const [p1, p2] = activePointers;

      // Initialize pinch gesture immediately
      const initialDistance = getDistance(p1, p2);
      initialPinchDistanceRef.current = initialDistance;
      lastPinchDistanceRef.current = initialDistance;
      initialScaleRef.current = lastScaleRef.current;
      isGestureActiveRef.current = true;
      gestureStartTimeRef.current = Date.now();

      const clientMidpoint = getMidpoint(p1, p2);
      pinchStartBoardMidpointRef.current = {
        x: (clientMidpoint.x - viewportRef.current.left - positionRef.current.x) / lastScaleRef.current,
        y: (clientMidpoint.y - viewportRef.current.top - positionRef.current.y) / lastScaleRef.current,
      };
    }
  }, [positionRef, handleCanvasPointerMove, handleCanvasPointerUp, setIsGrabbing, isAnyItemDragging]); // Removed scale dependency to avoid stale closures

  const resetView = useCallback(() => {
    if (typeof window !== 'undefined') {
        const currentVpWidth = viewportRef.current.width || window.innerWidth;
        const currentVpHeight = viewportRef.current.height || window.innerHeight;

        const centeredPos = calculateCenteredPosition(currentVpWidth, currentVpHeight, initialScale);
        
        applyTransform(initialScale, centeredPos);
    }

    setIsGrabbing(false);
    isPanningOrPinchingRef.current = false;
    initialPinchDistanceRef.current = 0;
    lastPinchDistanceRef.current = 0;
    isGestureActiveRef.current = false;
    gestureStartTimeRef.current = 0;
    activePointersRef.current.clear();

    // Clean up any event listeners
    window.removeEventListener('pointermove', handleCanvasPointerMove);
    window.removeEventListener('pointerup', handleCanvasPointerUp);
    window.removeEventListener('pointercancel', handleCanvasPointerUp);
    window.removeEventListener('touchmove', preventDefaultTouchMove);
  }, [initialScale, handleCanvasPointerMove, handleCanvasPointerUp, applyTransform]);

  // Initialize on first mount or when initialScale changes
  useEffect(() => {
    if (!isInitializedRef.current && typeof window !== 'undefined') {
      const currentVpWidth = viewportRef.current.width || window.innerWidth;
      const currentVpHeight = viewportRef.current.height || window.innerHeight;
      const currentVpLeft = viewportRef.current.left || 0;
      const currentVpTop = viewportRef.current.top || 0;

      viewportRef.current = {
          width: currentVpWidth,
          height: currentVpHeight,
          left: currentVpLeft,
          top: currentVpTop
      };
      
      const centeredPos = calculateCenteredPosition(
        currentVpWidth,
        currentVpHeight,
        initialScale
      );
      
      setPositionInternal(centeredPos);
      lastPositionRef.current = centeredPos;
      lastScaleRef.current = initialScale;
      isInitializedRef.current = true;
    }
  }, [initialScale, viewportRef]); 



  // Cleanup window listeners on unmount
  useEffect(() => {
    const currentHandleCanvasPointerMove = handleCanvasPointerMove;
    const currentHandleCanvasPointerUp = handleCanvasPointerUp;
    const currentActivePointers = Array.from(activePointersRef.current.values());

    return () => {
      if (transformTimeoutRef.current) {
        cancelAnimationFrame(transformTimeoutRef.current);
      }

      window.removeEventListener('pointermove', currentHandleCanvasPointerMove);
      window.removeEventListener('pointerup', currentHandleCanvasPointerUp);
      window.removeEventListener('pointercancel', currentHandleCanvasPointerUp);
      window.removeEventListener('touchmove', preventDefaultTouchMove);

      currentActivePointers.forEach(p => {
        try {
          if (p.target instanceof Element && typeof p.target.releasePointerCapture === 'function' && p.target.hasPointerCapture(p.pointerId)) {
            p.target.releasePointerCapture(p.pointerId);
          }
        } catch (e) {
          // Ignore errors during cleanup
        }
      });
      activePointersRef.current.clear();
    };
  }, [handleCanvasPointerMove, handleCanvasPointerUp]);

  return {
    scale,
    position,
    isGrabbing,
    setScale,
    setPosition,
    setScaleAndPosition,
    handleWheel,
    handleCanvasPointerDown: handleCanvasElementPointerDown,
    resetView,
    boardDimensions: { width: BOARD_WIDTH, height: BOARD_HEIGHT },
  };
}; 