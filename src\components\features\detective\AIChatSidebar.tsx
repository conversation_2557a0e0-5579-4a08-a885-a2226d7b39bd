import React, { useState, useRef, useEffect, use<PERSON><PERSON>back, forwardRef, useImperativeHandle } from 'react';
import { X, Send, Bot, Loader2, Book, Brain, RefreshCw, Search, Check, ChevronDown, ChevronUp, Globe, PlusCircle, Link, Eye, Trash2, Edit3, Plus, RotateCcw, Square } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import styles from '@/styles/Comment.module.css';
import type { Tables } from '@/lib/database.types';
import { Button } from '@/components/ui/button';
import AIChatInput, { ContextItem } from './AIChatInput';
import { useBoard } from '@/context/BoardContext';
import { AI_CONFIG } from '@/config/ai-limits';

// Define types for the new structured system
interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
}

interface ToolUpdate {
  type: 'tool_update';
  tool_call_id: string;
  tool_name: string;
  status: 'executing' | 'completed' | 'failed';
  result?: any;
  error?: string;
}

interface ContentUpdate {
  type: 'content';
  content: string;
}

interface TimelineItem {
  type: 'tool' | 'text';
  timestamp: number;
  content?: string; // For text items
  toolExecution?: ToolExecution; // For tool items
  finished?: boolean; // For text items
}

interface StreamChunk {
  type: 'tool_update' | 'content' | 'done' | 'error' | 'thinking';
  content?: string;
  finished?: boolean; // For content chunks - whether this completes the response
  incremental?: boolean; // For content chunks - whether this is an incremental chunk (true) or completion signal (false)
  tool_call_id?: string;
  tool_name?: string;
  status?: 'executing' | 'completed' | 'failed';
  result?: any;
  error?: string;
  thinking?: boolean; // For thinking chunks - whether the AI is thinking
}

interface ToolExecution {
  id: string;
  toolName: string;
  parameters: any;
  status: 'executing' | 'completed' | 'failed';
  result?: any;
  error?: string;
  timestamp: Date;
  displayName: string;
  icon: React.ReactNode;
}

interface AIChatSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  onAddInsight: (content: string) => Promise<void>;
  onCreateElements?: (elements: any[], connections: any[]) => Promise<void>;
  onProcessAIOperations?: (operations: any[], operationType: 'update' | 'delete') => Promise<void>;
  boardId?: string;
  sidebarWidth?: number;
  onResizeStart?: () => void;
}

export interface AIChatSidebarRef {
  addContextMessage: (content: string) => void;
  removeContextItem: (id: string) => void;
}

interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  isComplete?: boolean;
  createdAt?: Date;
  toolExecutions?: ToolExecution[];
  timeline?: TimelineItem[]; // New: ordered timeline of all events
  id?: string;
  isThinking?: boolean;
  thinkingStartTime?: Date;
}

// Tool icon mapping
const getToolIcon = (toolName: string) => {
  switch (toolName) {
    case 'exa_search':
    case 'exa_answer':
    case 'exa_research':
      return <Globe size={14} />;
    case 'create_element':
      return <PlusCircle size={14} />;
    case 'create_connection':
      return <Link size={14} />;
    case 'read_board':
      return <Eye size={14} />;
    case 'update_element':
      return <Edit3 size={14} />;
    case 'delete_element':
    case 'delete_connection':
      return <Trash2 size={14} />;
    case 'move_viewport':
      return <Eye size={14} />;
    default:
      return <Bot size={14} />;
  }
};

// Tool display name mapping
const getToolDisplayName = (toolName: string, parameters: any, result?: any) => {
  switch (toolName) {
    case 'exa_search':
      return `Searching web`;
    case 'exa_answer':
      return `Getting answer`;
    case 'exa_research':
      return `Researching topic`;
    case 'create_element':
      const elementType = parameters?.type || result?.element?.type || result?.type;
      return elementType ? `Creating ${elementType}` : `Creating element`;
    case 'create_connection':
      return `Connecting elements`;
    case 'read_board':
      return `Reading board state`;
    case 'update_element':
      return `Updating element`;
    case 'delete_element':
      return `Deleting element`;
    case 'delete_connection':
      return `Deleting connection`;
    case 'move_viewport':
      return `Moving viewport`;
    default:
      return `Executing ${toolName || 'tool'}`;
  }
};

// Tool execution component
const ToolExecutionCard: React.FC<{ execution: ToolExecution }> = ({ execution }) => {
  // Skip rendering if tool execution is malformed
  if (!execution.toolName || execution.toolName === 'undefined' || !execution.displayName) {
    return null;
  }

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-md p-3 mb-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            {execution.icon}
            <span className="text-sm font-medium text-gray-700">
              {execution.displayName}
            </span>
          </div>
          <div className="flex items-center">
            {execution.status === 'executing' && (
              <Loader2 size={14} className="animate-spin text-blue-500" />
            )}
            {execution.status === 'completed' && (
              <Check size={14} className="text-green-500" />
            )}
            {execution.status === 'failed' && (
              <X size={14} className="text-red-500" />
            )} 
          </div>
        </div>
      </div>
      
      {execution.status === 'failed' && execution.error && (
        <div className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded">
          Error: {execution.error}
        </div>
      )}
      
      {execution.status === 'completed' && execution.result && (
        <div className="mt-2">
          <ToolResultDisplay toolName={execution.toolName} result={execution.result} />
        </div>
      )}
    </div>
  );
};

// Tool result display component
const ToolResultDisplay: React.FC<{ toolName: string; result: any }> = ({ toolName, result }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!result) {
    return <div className="text-gray-600">Tool executed successfully</div>;
  }

  switch (toolName) {
    case 'exa_search':
      return (
        <div>
          <div className="font-medium mb-2">Search Results ({result.totalResults})</div>
          {result.results?.slice(0, 3).map((searchResult: any, i: number) => (
            <div key={i} className="mb-2 pb-2 border-b border-gray-100 last:border-b-0">
              <div className="font-medium text-blue-600">
                <a href={searchResult.url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                  {searchResult.title}
                </a>
              </div>
              <div className="text-gray-600 mt-1">{searchResult.snippet}</div>
            </div>
          ))}
          {result.results?.length > 3 && (
            <div className="text-gray-500 text-center">... and {result.results.length - 3} more results</div>
          )}
        </div>
      );

    case 'exa_answer':
      return (
        <div>
          <div className="font-medium mb-2">Answer</div>
          <div className="mb-3">{result.answer}</div>
          {result.citations?.length > 0 && (
            <>
              <div className="font-medium mb-2">Sources ({result.totalCitations})</div>
              {result.citations.slice(0, 2).map((citation: any, i: number) => (
                <div key={i} className="mb-1">
                  <a href={citation.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline text-xs">
                    {citation.title}
                  </a>
                </div>
              ))}
            </>
          )}
        </div>
      );

    case 'exa_research':
      return (
        <div>
          <div 
            className="cursor-pointer flex items-center gap-2 text-blue-600 hover:text-blue-800"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <span>Research completed</span>
            {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </div>
          {isExpanded && result.researchContent && (
            <div className="mt-2 p-3 bg-gray-50 rounded text-sm text-gray-700 max-h-64 overflow-y-auto">
              {result.researchContent}
            </div>
          )}
        </div>
      );

    case 'create_element':
      return (
        <div className="text-green-600">Element created</div>
      );

    case 'create_connection':
      return (
        <div className="text-green-600">Connection created</div>
      );

    case 'read_board':
      return (
        <div className="text-gray-600">
          Found {result.elementCount} elements with {result.connectionCount} connections
        </div>
      );

    case 'update_element':
      return (
        <div className="text-blue-600">Element updated</div>
      );

    case 'delete_element':
      return (
        <div className="text-red-600">Element deleted</div>
      );

    case 'delete_connection':
      return (
        <div className="text-red-600">Connection deleted</div>
      );

    default:
      return (
        <div>
          <div className="font-medium mb-2">Tool Result</div>
          <div className="text-xs text-gray-600">{result.message || 'Tool executed successfully'}</div>
        </div>
      );
  }
};

// Format AI response with proper markdown rendering
const formatAIResponse = (text: string): React.ReactNode => {
  // First, unescape any JSON escape characters that might still be in the content
  const unescapedText = text.replace(/\\n/g, '\n');
    
  const lines = unescapedText.split('\n');
  
  return lines.map((line, index) => {
    let processedLine = line;
    
    // Handle bold text **text** -> <strong>text</strong>
    processedLine = processedLine.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Handle italic text *text* -> <em>text</em>
    processedLine = processedLine.replace(/\*([^*]+)\*/g, '<em>$1</em>');
    
    // Handle links [text](url) -> <a href="url">text</a>
    processedLine = processedLine.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">$1</a>');
    
    return (
      <span key={index}>
        <span dangerouslySetInnerHTML={{ __html: processedLine }} />
        {index < lines.length - 1 && <br />}
      </span>
    );
  });
};

// Add typing indicator component
const TypingIndicator = () => (
  <div className="flex items-center space-x-1">
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-noir-accent rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
      <div className="w-2 h-2 bg-noir-accent rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
      <div className="w-2 h-2 bg-noir-accent rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
    </div>
    <span className="text-black/70 text-sm ml-2">AI is thinking and researching...</span>
  </div>
);

// Add thinking indicator component with timer
const ThinkingIndicator = ({ startTime }: { startTime: Date }) => {
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime.getTime()) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  return (
    <div className="flex items-center space-x-2">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '200ms' }}></div>
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '400ms' }}></div>
      </div>
      <span className="text-black/70 text-sm">
        AI is thinking... {elapsedTime}s
      </span>
    </div>
  );
};

const AIChatSidebar = forwardRef<AIChatSidebarRef, AIChatSidebarProps>(({
  isOpen,
  onClose,
  onAddInsight,
  onCreateElements,
  onProcessAIOperations,
  boardId,
  sidebarWidth = 400,
  onResizeStart
}, ref) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [remainingMessages, setRemainingMessages] = useState<number>(AI_CONFIG.DAILY_MESSAGE_LIMIT);
  const [resetTime, setResetTime] = useState<Date | null>(null);
  const messageLimit = AI_CONFIG.DAILY_MESSAGE_LIMIT;
  const [contextItems, setContextItems] = useState<ContextItem[]>([]);
  const [currentChatNumber, setCurrentChatNumber] = useState<number>(0);
  const [availableChats, setAvailableChats] = useState<Array<{number: number, messageCount: number, lastActivity: Date}>>([]);
  const [showChatHistory, setShowChatHistory] = useState(false);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [hasInitializedChat, setHasInitializedChat] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const resizeHandleRef = useRef<HTMLDivElement>(null);
  
  // Keep a live reference to the current board state for AI API calls
  const currentBoardDataRef = useRef<{
    elements: any[];
    connections: any[];
    strokes: any[];
  } | null>(null);

  const { board, setPosition, setScale } = useBoard();

  // Update the current board data ref whenever the board changes
  useEffect(() => {
    console.log(`[AIChatSidebar] Updating currentBoardDataRef: ${board.elements?.length || 0} elements, ${board.connections?.length || 0} connections`);
    currentBoardDataRef.current = {
      elements: board.elements || [],
      connections: board.connections || [],
      strokes: board.strokes || []
    };
  }, [board.elements, board.connections, board.strokes]);

  // Define colors for different context items
  const contextColors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

  // Helper function to format role with chat number
  const formatRoleForDB = (role: 'user' | 'assistant') => {
    if (currentChatNumber === 0) {
      return role;
    }
    return `${role}+${currentChatNumber}`;
  };

  // Helper function to check if a role matches the current chat
  const isRoleFromCurrentChat = (role: string) => {
    if (currentChatNumber === 0) {
      return role === 'user' || role === 'assistant';
    }
    return role === `user+${currentChatNumber}` || role === `assistant+${currentChatNumber}`;
  };

  // Helper function to calculate remaining messages across all chats
  const calculateRemainingMessages = useCallback((allMessages: any[]) => {
    const twentyFourHoursAgo = Date.now() - 24 * 60 * 60 * 1000;
    
    const userMessagesToday = allMessages
      .filter(row => {
        const baseRole = row.role.includes('+') ? row.role.split('+')[0] : row.role;
        return baseRole === 'user' && new Date(row.created_at).getTime() >= twentyFourHoursAgo;
      })
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    
    const countToday = userMessagesToday.length;
    const remaining = Math.max(0, messageLimit - countToday);
    
    setRemainingMessages(remaining);

    if (remaining === 0 && countToday >= messageLimit) {
      const thirdMessageTime = new Date(userMessagesToday[messageLimit - 1].created_at).getTime();
      const resetDate = new Date(thirdMessageTime + 24 * 60 * 60 * 1000);
      setResetTime(resetDate);
    } else {
      setResetTime(null);
    }
  }, [messageLimit]);

  // Function to stop generating
  const stopGenerating = useCallback(() => {
    if (abortController) {
      abortController.abort();
      setIsLoading(false);
      toast.info('Response generation stopped.');
    }
  }, [abortController]);

  // Reset chat initialization when sidebar closes
  useEffect(() => {
    if (!isOpen) {
      setHasInitializedChat(false);
    }
  }, [isOpen]);

  // Load persisted chat messages
  useEffect(() => {
    const loadChatHistory = async () => {
      if (!boardId || boardId === 'new') {
        setMessages([]);
        setRemainingMessages(AI_CONFIG.DAILY_MESSAGE_LIMIT);
        setResetTime(null);
        setCurrentChatNumber(0);
        setAvailableChats([]);
        setHasInitializedChat(false);
        return;
      }

      if (!isOpen) {
        return;
      }

      try {
        const response = await fetch(`/api/ai/chat?boardId=${boardId}`);
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to load chat history: ${response.statusText}`);
        }
        const data = await response.json();

        if (Array.isArray(data)) {
          // Analyze all messages to find available chats
          const chatSessions: { [key: number]: { messages: any[], lastActivity: Date } } = {};
          
          data.forEach((row: any) => {
            const chatNumber = row.role.includes('+') ? parseInt(row.role.split('+')[1]) : 0;
            if (!chatSessions[chatNumber]) {
              chatSessions[chatNumber] = { messages: [], lastActivity: new Date(row.created_at) };
            }
            chatSessions[chatNumber].messages.push(row);
            const createdAt = new Date(row.created_at);
            if (createdAt > chatSessions[chatNumber].lastActivity) {
              chatSessions[chatNumber].lastActivity = createdAt;
            }
          });

          // Update available chats
          const chats = Object.keys(chatSessions).map(chatNum => ({
            number: parseInt(chatNum),
            messageCount: chatSessions[parseInt(chatNum)].messages.length,
            lastActivity: chatSessions[parseInt(chatNum)].lastActivity
          })).sort((a, b) => b.number - a.number);
          
          setAvailableChats(chats);
          
          // Determine which chat to display
          let targetChatNumber = currentChatNumber;
          
          // On first initialization, set target chat to the most recently active one
          if (!hasInitializedChat && chats.length > 0) {
            const mostRecentChat = chats.reduce((latest, chat) => 
              chat.lastActivity > latest.lastActivity ? chat : latest
            );
            targetChatNumber = mostRecentChat.number;
            setCurrentChatNumber(targetChatNumber);
            setHasInitializedChat(true);
          }
          
          // Filter messages to only include those from the target chat
          const isRoleFromTargetChat = (role: string) => {
            if (targetChatNumber === 0) {
              return role === 'user' || role === 'assistant';
            }
            return role === `user+${targetChatNumber}` || role === `assistant+${targetChatNumber}`;
          };
          
          const filteredData = (data as Tables<'ai_chat_messages'>[]).filter(row => 
            isRoleFromTargetChat(row.role)
          );
          
          const history: Message[] = filteredData.map((row) => {
            const baseRole = row.role.includes('+') ? row.role.split('+')[0] : row.role;
            const message: Message = {
              id: row.id,
              role: baseRole as 'user' | 'assistant',
              content: row.content,
              isComplete: true,
              createdAt: new Date(row.created_at)
            };
            
            // Reconstruct tool executions from raw_content for assistant messages
            if (baseRole === 'assistant' && row.raw_content) {
              try {
                const rawContent = typeof row.raw_content === 'string' 
                  ? JSON.parse(row.raw_content) 
                  : row.raw_content;
                
                // Check for new timeline format first
                if (rawContent.timeline && Array.isArray(rawContent.timeline)) {
                  // Use the new timeline format
                  message.timeline = rawContent.timeline.map((item: any) => ({
                    ...item,
                    toolExecution: item.toolExecution ? {
                      ...item.toolExecution,
                      timestamp: new Date(item.toolExecution.timestamp),
                      icon: getToolIcon(item.toolExecution.toolName),
                      displayName: item.toolExecution.displayName || getToolDisplayName(
                        item.toolExecution.toolName, 
                        item.toolExecution.parameters || {}, 
                        item.toolExecution.result
                      )
                    } : undefined
                  }));
                  
                  // Use totalContent if available, otherwise reconstruct from timeline
                  if (rawContent.totalContent) {
                    message.content = rawContent.totalContent;
                  } else {
                    message.content = rawContent.timeline
                      .filter((item: any) => item.type === 'text' && item.content)
                      .map((item: any) => item.content)
                      .join('');
                  }
                  
                  message.isComplete = rawContent.isComplete !== undefined ? rawContent.isComplete : true;
                }
                // Fall back to legacy format for backwards compatibility
                else if (rawContent.toolExecutions && Array.isArray(rawContent.toolExecutions)) {
                  message.toolExecutions = rawContent.toolExecutions
                    .filter((execution: any) => execution.toolName && execution.toolName !== 'undefined')
                    .map((execution: any) => ({
                      ...execution,
                      timestamp: execution.timestamp ? new Date(execution.timestamp) : new Date(),
                      icon: getToolIcon(execution.toolName),
                      displayName: execution.displayName || getToolDisplayName(execution.toolName, execution.parameters || {}, execution.result)
                    }));
                  
                  if (rawContent.isComplete !== undefined) {
                    message.isComplete = rawContent.isComplete;
                  }
                }
              } catch (error) {
                console.warn('[AIChatSidebar] Failed to parse raw_content for message:', row.id, error);
              }
            }
            
            return message;
          });

          setMessages(history);
          calculateRemainingMessages(data);
        }
      } catch (err: any) {
        console.error('[AIChatSidebar] Error loading chat history:', err);
        toast.error('Failed to load chat history.');
        setRemainingMessages(AI_CONFIG.DAILY_MESSAGE_LIMIT);
        setResetTime(null);
      }
    };

    loadChatHistory();
  }, [isOpen, boardId, calculateRemainingMessages]);

  // Scroll to bottom of chat when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Cleanup abort controller on unmount
  useEffect(() => {
    return () => {
      if (abortController) {
        abortController.abort();
      }
    };
  }, [abortController]);

  // New structured AI response handler
  const fetchStructuredAIResponse = async (
    chatMessages: Message[],
    setStreamingMessage: (content: string, toolExecutions?: ToolExecution[], timeline?: TimelineItem[], isComplete?: boolean, isThinking?: boolean, thinkingStartTime?: Date) => void
  ) => {
    // Create abort controller
    const controller = new AbortController();
    setAbortController(controller);
    
    try {
      // Include context items in the messages
      const contextMessages = contextItems.map(item => ({
        role: 'user' as const,
        content: `Context: ${item.content}`
      }));
      
      const messagesToSend = [
        ...contextMessages,
        ...chatMessages.map(({ role, content }) => ({ role, content }))
      ];

      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: messagesToSend,
          boardId: boardId,
          boardData: boardId && boardId !== 'new' ?
            (() => {
              // CRITICAL FIX: Always get fresh board data from current board state
              // This ensures the AI sees manually added elements, not just the initial snapshot
              const freshBoardData = {
                elements: board.elements || [],
                connections: board.connections || [],
                strokes: board.strokes || []
              };
              console.log(`[AIChatSidebar] Sending FRESH board data to AI: ${freshBoardData.elements.length} elements, ${freshBoardData.connections.length} connections`);
              console.log(`[AIChatSidebar] Element IDs being sent:`, freshBoardData.elements.map(e => e.id));
              return freshBoardData;
            })() :
            undefined,
          model: 'standard',
          stream: true
        }),
        signal: controller.signal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `AI API request failed with status ${response.status}`);
      }

      if (!response.body) {
        throw new Error("The response body is empty.");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let currentContent = '';
      let toolExecutions: ToolExecution[] = [];
      let timeline: TimelineItem[] = [];
      let allMessages: Message[] = [];
      let done = false;

      while (!done) {
        const { value, done: doneReading } = await reader.read();
        done = doneReading;
        
        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const dataContent = line.substring(6).trim();
              // Debug: log raw stream content for debugging
              if (dataContent !== '[DONE]') {
              }
              if (dataContent === '[DONE]') {
                done = true;
                break;
              }
              
              try {
                const parsedChunk: StreamChunk = JSON.parse(dataContent);
                
                if (parsedChunk.type === 'content') {
                  const rawContent = parsedChunk.content || '';
                  const finished = parsedChunk.finished !== false;
                  const isIncremental = (parsedChunk as any).incremental !== false;

                  if (
                    timeline.length > 0 &&
                    timeline[timeline.length - 1].type === 'text' &&
                    !timeline[timeline.length - 1].finished
                  ) {
                    if (isIncremental && rawContent) {
                      // Accumulate incremental content
                      timeline[timeline.length - 1].content += rawContent;
                    } else if (!isIncremental) {
                      // This is a completion signal, just mark as finished
                      timeline[timeline.length - 1].finished = finished;
                    }
                    timeline[timeline.length - 1].finished = finished;
                  } else if (rawContent || !isIncremental) {
                    // Start a new timeline item (only if there's content or it's a completion signal)
                    timeline.push({
                      type: 'text',
                      timestamp: Date.now(),
                      content: rawContent,
                      finished: finished,
                    });
                  }

                  // For display, join all timeline items' content in order
                  setStreamingMessage(
                    timeline.map(item => (item.type === 'text' ? item.content : '')).join(''),
                    [...toolExecutions],
                    [...timeline],
                    finished,
                    false, // Not thinking when we have content
                    undefined
                  );

                  if (finished) {
                    done = true;
                  }
                } else if (parsedChunk.type === 'tool_update') {
                  // Skip tool updates that don't have proper tool_name or tool_call_id
                  if (!parsedChunk.tool_name || !parsedChunk.tool_call_id || 
                      parsedChunk.tool_name === 'undefined' || parsedChunk.tool_call_id === 'undefined') {
                    console.warn('Skipping malformed tool update:', parsedChunk);
                    continue;
                  }
                  
                  // Now process the current tool_update for the current (possibly new) placeholder
                  let existingIndex = toolExecutions.findIndex(t => t.id === parsedChunk.tool_call_id);
                  
                  if (existingIndex >= 0) {
                    toolExecutions[existingIndex] = {
                      ...toolExecutions[existingIndex],
                      status: parsedChunk.status!,
                      result: parsedChunk.result,
                      error: parsedChunk.error,
                      displayName: getToolDisplayName(parsedChunk.tool_name!, toolExecutions[existingIndex].parameters, parsedChunk.result)
                    };
                    
                    // Update the corresponding timeline item
                    const timelineIndex = timeline.findIndex(item => 
                      item.type === 'tool' && item.toolExecution?.id === parsedChunk.tool_call_id
                    );
                    if (timelineIndex >= 0) {
                      timeline[timelineIndex].toolExecution = toolExecutions[existingIndex];
                    }
                  } else {
                    const newToolExecution = {
                      id: parsedChunk.tool_call_id!,
                      toolName: parsedChunk.tool_name!,
                      parameters: {},
                      status: parsedChunk.status!,
                      result: parsedChunk.result,
                      error: parsedChunk.error,
                      timestamp: new Date(),
                      displayName: getToolDisplayName(parsedChunk.tool_name!, {}, parsedChunk.result),
                      icon: getToolIcon(parsedChunk.tool_name!)
                    };
                    
                    toolExecutions.push(newToolExecution);
                    
                    // Add to timeline
                    timeline.push({
                      type: 'tool',
                      timestamp: Date.now(),
                      toolExecution: newToolExecution
                    });
                  }
                  
                  // Update streaming message with current timeline
                  setStreamingMessage(currentContent, [...toolExecutions], [...timeline], false, false, undefined);
                  
                } else if (parsedChunk.type === 'thinking') {
                  // Handle thinking state updates
                  const isThinking = (parsedChunk as any).thinking;
                  setStreamingMessage(
                    currentContent,
                    [...toolExecutions],
                    [...timeline],
                    false,
                    isThinking,
                    isThinking ? new Date() : undefined
                  );

                } else if (parsedChunk.type === 'done') {
                  done = true;
                  break;

                } else if (parsedChunk.type === 'error') {
                  throw new Error(parsedChunk.error || 'AI request failed');
                }
              } catch (error: any) {
                console.warn('Failed to parse stream chunk JSON:', dataContent, error);
              }
            }
          }
        }
      }

      // Stream is complete - create the final message and ensure it's marked as complete
      if (currentContent || toolExecutions.length > 0 || timeline.length > 0) {
        const finalMessage: Message = {
          role: 'assistant',
          content: currentContent,
          isComplete: true,
          toolExecutions: toolExecutions.length > 0 ? [...toolExecutions] : undefined,
          timeline: timeline.length > 0 ? [...timeline] : undefined
        };
        
        // Replace the streaming message with the final complete message
        setMessages(prev => prev.slice(0, prev.length - 1).concat(finalMessage));
        
        allMessages.push(finalMessage);
      }

      return {
        content: currentContent,
        isComplete: true,
        toolExecutions: toolExecutions,
        timeline: timeline
      };
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('AI response generation was cancelled');
        return {
          content: 'Response generation was cancelled.',
          isComplete: true,
          toolExecutions: [],
          timeline: []
        };
      }
      console.error('Error getting structured AI response:', error);
      toast.error(error.message || 'Failed to get AI response. Please try again.');
      throw error;
    } finally {
      setAbortController(null);
    }
  };

  // Handle submit from input component  
  const handleInputSubmit = useCallback(async (message: string, currentContextItems: ContextItem[]) => {
    if (remainingMessages <= 0) {
      toast.error('Message limit reached for today on this board.');
      return;
    }

    // Cancel any existing request
    if (abortController) {
      abortController.abort();
    }

    const userMessage: Message = { role: 'user', content: message };
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    const messagesForAICall = [...messages, userMessage];
    const loadingPlaceholder: Message = {
      role: 'assistant',
      content: '',
      isComplete: false
    };
    setMessages(prevMessages => [...prevMessages, loadingPlaceholder]);

    // Persist user message
    if (boardId && boardId !== 'new') { 
      fetch('/api/ai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ boardId, role: formatRoleForDB('user'), content: message })
      }).then(async (response) => {
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          if (response.status === 429) {
            toast.error(errorData.error || 'Message limit reached for today.');
            setRemainingMessages(0);
          } else {
            toast.error(errorData.error || 'Failed to save your message.');
          }
          throw new Error(errorData.error || `API error: ${response.status}`);
        } else {
          setRemainingMessages(prev => Math.max(0, prev - 1));
        }
      }).catch(err => {
        if (!(err instanceof Error && err.message.includes('API error'))) {
          console.error('[AIChatSidebar] Network error saving user message:', err);
          toast.error('Network error. Failed to send message.');
        }
      });
    } else if (boardId === 'new') {
      setRemainingMessages(prev => Math.max(0, prev - 1));
    }

    // Track processed tool executions to avoid duplicates during streaming
    const processedToolIds = new Set<string>();
    
    // Helper function to process new tool executions
    const processNewToolExecutions = async (toolExecutions: ToolExecution[]) => {
      const newElements: any[] = [];
      const newConnections: any[] = [];
      const updateOperations: any[] = [];
      const deleteOperations: any[] = [];

      toolExecutions?.forEach(execution => {

        // Only process completed executions we haven't seen before
        if (execution.status === 'completed' && execution.result && !processedToolIds.has(execution.id)) {
          processedToolIds.add(execution.id);

          if (execution.toolName === 'create_element' && execution.result.element) {
            const elementData = {
              id: execution.result.element.id,
              type: execution.result.element.type,
              content: execution.result.element.content,
              title: execution.result.element.title,
              url: execution.result.element.url,
              position_x: execution.result.element.position.x,
              position_y: execution.result.element.position.y
            };
            newElements.push(elementData);
          } else if (execution.toolName === 'create_connection' && execution.result.connection) {
            const connectionData = {
              from_id: execution.result.connection.fromId,
              to_id: execution.result.connection.toId,
              label: execution.result.connection.label
            };
            console.log(`[AIChatSidebar] Processing connection: ${connectionData.from_id} -> ${connectionData.to_id}${connectionData.label ? ` (${connectionData.label})` : ''}`);
            newConnections.push(connectionData);
          } else if (execution.toolName === 'update_element' && execution.result.operation) {
            updateOperations.push({
              type: 'element',
              ...execution.result.operation
            });
          } else if (execution.toolName === 'delete_element' && execution.result.operation) {
            deleteOperations.push({
              type: 'element',
              ...execution.result.operation
            });
          } else if (execution.toolName === 'delete_connection' && execution.result.operation) {
            deleteOperations.push({
              type: 'connection',
              ...execution.result.operation
            });
          }
        } else if (processedToolIds.has(execution.id)) {
        }
      });
      
      // Create elements immediately if we have new ones
      if ((newElements.length > 0 || newConnections.length > 0) && onCreateElements) {
        try {
          console.log(`[AIChatSidebar] ===== CALLING onCreateElements =====`);
          console.log(`[AIChatSidebar] Creating ${newElements.length} elements and ${newConnections.length} connections during streaming`);
          console.log(`[AIChatSidebar] Elements to create:`, newElements.map(el => `${el.id}(${el.type})`));
          console.log(`[AIChatSidebar] Timestamp:`, Date.now());

          await onCreateElements(newElements, newConnections);

          console.log(`[AIChatSidebar] ===== onCreateElements COMPLETED =====`);
          console.log(`[AIChatSidebar] Successfully created ${newElements.length} elements and ${newConnections.length} connections`);
          console.log(`[AIChatSidebar] Timestamp:`, Date.now());

          // Note: Board data updates are now handled at the AI route level
          // The mutable board data is updated in memory when elements are created
          // ensuring subsequent read_board calls see the newly created elements
          console.log('[AIChatSidebar] AI elements created successfully - board data updated in memory');
        } catch (error) {
          console.error('[AIChatSidebar] Failed to create elements during streaming:', error);
          toast.error('Failed to add some elements to the board. Please try again.');
        }
      }

      // Process update operations
      if (updateOperations.length > 0 && onProcessAIOperations) {
        try {
          console.log(`[AIChatSidebar] Processing ${updateOperations.length} update operations`);
          await onProcessAIOperations(updateOperations, 'update');
          console.log('[AIChatSidebar] AI update operations completed successfully');
        } catch (error) {
          console.error('[AIChatSidebar] Failed to process update operations:', error);
          toast.error('Failed to update some elements. Please try again.');
        }
      }

      // Process delete operations
      if (deleteOperations.length > 0 && onProcessAIOperations) {
        try {
          console.log(`[AIChatSidebar] Processing ${deleteOperations.length} delete operations`);
          await onProcessAIOperations(deleteOperations, 'delete');
          console.log('[AIChatSidebar] AI delete operations completed successfully');
        } catch (error) {
          console.error('[AIChatSidebar] Failed to process delete operations:', error);
          toast.error('Failed to delete some elements. Please try again.');
        }
      }
    };

    try {
      const responseData = await fetchStructuredAIResponse(
        messagesForAICall,
        (streamingContent, toolExecutions, timeline, isComplete, isThinking, thinkingStartTime) => {
          // Process any new tool executions immediately during streaming (non-blocking)
          processNewToolExecutions(toolExecutions || []).catch(error => {
            console.error('[AIChatSidebar] Error processing tool executions during streaming:', error);
          });
          setMessages(prev => prev.slice(0, prev.length - 1).concat({
            role: 'assistant',
            content: streamingContent,
            isComplete: isComplete || false,
            toolExecutions: toolExecutions || [],
            timeline: timeline || [],
            isThinking: isThinking || false,
            thinkingStartTime: thinkingStartTime
          }));
        }
      );
      
      // Process any final tool executions that might not have been caught during streaming
      await processNewToolExecutions(responseData.toolExecutions || []);
      
      // Save assistant message to database with timeline information
      if (boardId && boardId !== 'new' && (responseData.content || (responseData.timeline && responseData.timeline.length > 0))) {
        try {
          const timelineForStorage = responseData.timeline?.map(item => ({
            ...item,
            // Remove React elements from toolExecution for JSON serialization
            toolExecution: item.toolExecution ? {
              ...item.toolExecution,
              icon: undefined, // Remove React element
              timestamp: item.toolExecution.timestamp.toISOString() // Convert to string
            } : undefined
          })) || [];
          
          await fetch('/api/ai/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
              boardId, 
              role: formatRoleForDB('assistant'), 
              content: '', // Empty content since we're using timeline in raw_content
              rawContent: {
                timeline: timelineForStorage,
                isComplete: true,
                totalContent: responseData.content || ''
              }
            })
          });
        } catch (error) {
          console.error('[AIChatSidebar] Failed to save assistant message:', error);
          // Don't show error to user as the message is still displayed locally
        }
      }
      
      // The final message is now handled by fetchStructuredAIResponse, so we don't need to add it here
    } catch (error: any) {
      if (error.name === 'AbortError') {
        // Handle abort case - remove the loading message
        setMessages(prev => prev.slice(0, prev.length - 1));
        console.log('AI response generation was cancelled by user');
      } else {
        console.error('Error in AI chat flow:', error);
        setMessages(prev => prev.slice(0, prev.length - 1));
      }
    } finally {
      setIsLoading(false);
    }
  }, [remainingMessages, messages, boardId, fetchStructuredAIResponse, onCreateElements, abortController]);

  const handleFocusContext = useCallback((contextId: string) => {
    window.dispatchEvent(new CustomEvent('focusContext', { detail: contextId }));
  }, []);

  const addContextMessage = useCallback((content: string) => {
    try {
      const contextItem: ContextItem = {
        id: `context-${Date.now()}`,
        content,
        color: contextColors[contextItems.length % contextColors.length],
        createdAt: new Date()
      };
      
      setContextItems(prev => [...prev, contextItem]);
      
    } catch (error) {
      console.error('[AIChatSidebar] Error in addContextMessage:', error);
      throw error; // Re-throw so the calling code can handle it
    }
  }, [contextItems.length]);

  const removeContextItem = useCallback((id: string) => {
    setContextItems(prev => prev.filter(item => item.id !== id));
  }, []);

  const startNewChat = useCallback(() => {
    setAvailableChats(prev => {
      const currentChatExists = prev.some(chat => chat.number === currentChatNumber);
      if (!currentChatExists && messages.length > 0) {
        const newChat = {
          number: currentChatNumber,
          messageCount: messages.length,
          lastActivity: new Date()
        };
        return [newChat, ...prev].sort((a, b) => b.number - a.number);
      }
      return prev;
    });
    
    setCurrentChatNumber(prev => prev + 1);
    setMessages([]);
    setContextItems([]);
    setShowChatHistory(false);
    
    // Reset board data ref to current board state when starting a new chat
    // This ensures the new conversation starts with the current board state
    if (currentBoardDataRef.current) {
      currentBoardDataRef.current = {
        elements: board.elements || [],
        connections: board.connections || [],
        strokes: board.strokes || []
      };
    }
  }, [currentChatNumber, messages.length, board.elements, board.connections, board.strokes]);

  const switchToChat = useCallback(async (chatNumber: number) => {
    setCurrentChatNumber(chatNumber);
    setMessages([]);
    setContextItems([]);
    setShowChatHistory(false);
    
    // Reset board data ref to current board state when switching chats
    // This ensures we start with a fresh view of the current board
    if (currentBoardDataRef.current) {
      currentBoardDataRef.current = {
        elements: board.elements || [],
        connections: board.connections || [],
        strokes: board.strokes || []
      };
    }
    
    // Reload messages for the selected chat
    if (!boardId || boardId === 'new') {
      return;
    }
    
    try {
      const response = await fetch(`/api/ai/chat?boardId=${boardId}`);
      if (!response.ok) {
        throw new Error('Failed to load chat history');
      }
      const data = await response.json();

      if (Array.isArray(data)) {
        // Filter messages for the target chat
        const isRoleFromTargetChat = (role: string) => {
          if (chatNumber === 0) {
            return role === 'user' || role === 'assistant';
          }
          return role === `user+${chatNumber}` || role === `assistant+${chatNumber}`;
        };
        
        const filteredData = (data as Tables<'ai_chat_messages'>[]).filter(row => 
          isRoleFromTargetChat(row.role)
        );
        
        const history: Message[] = filteredData.map((row) => {
          const baseRole = row.role.includes('+') ? row.role.split('+')[0] : row.role;
          const message: Message = {
            id: row.id,
            role: baseRole as 'user' | 'assistant',
            content: row.content,
            isComplete: true,
            createdAt: new Date(row.created_at)
          };
          
          // Reconstruct tool executions for assistant messages
          if (baseRole === 'assistant' && row.raw_content) {
            try {
              const rawContent = typeof row.raw_content === 'string' 
                ? JSON.parse(row.raw_content) 
                : row.raw_content;
              
              // Check for new timeline format first
              if (rawContent.timeline && Array.isArray(rawContent.timeline)) {
                // Use the new timeline format
                message.timeline = rawContent.timeline.map((item: any) => ({
                  ...item,
                  toolExecution: item.toolExecution ? {
                    ...item.toolExecution,
                    timestamp: new Date(item.toolExecution.timestamp),
                    icon: getToolIcon(item.toolExecution.toolName),
                    displayName: item.toolExecution.displayName || getToolDisplayName(
                      item.toolExecution.toolName, 
                      item.toolExecution.parameters || {}, 
                      item.toolExecution.result
                    )
                  } : undefined
                }));
                
                // Use totalContent if available, otherwise reconstruct from timeline
                if (rawContent.totalContent) {
                  message.content = rawContent.totalContent;
                } else {
                  message.content = rawContent.timeline
                    .filter((item: any) => item.type === 'text' && item.content)
                    .map((item: any) => item.content)
                    .join('');
                }
                
                message.isComplete = rawContent.isComplete !== undefined ? rawContent.isComplete : true;
              }
              // Fall back to legacy format for backwards compatibility
              else if (rawContent.toolExecutions && Array.isArray(rawContent.toolExecutions)) {
                message.toolExecutions = rawContent.toolExecutions
                  .filter((execution: any) => execution.toolName && execution.toolName !== 'undefined')
                  .map((execution: any) => ({
                    ...execution,
                    timestamp: execution.timestamp ? new Date(execution.timestamp) : new Date(),
                    icon: getToolIcon(execution.toolName),
                    displayName: execution.displayName || getToolDisplayName(execution.toolName, execution.parameters || {}, execution.result)
                  }));
                
                if (rawContent.isComplete !== undefined) {
                  message.isComplete = rawContent.isComplete;
                }
              }
            } catch (error) {
              console.warn('[AIChatSidebar] Failed to parse raw_content for message:', row.id, error);
            }
          }
          
          return message;
        });

        setMessages(history);
      }
    } catch (error) {
      console.error('[AIChatSidebar] Error switching to chat:', error);
      toast.error('Failed to load chat messages.');
    }
  }, [boardId]);

  useImperativeHandle(ref, () => ({
    addContextMessage,
    removeContextItem
  }));

  if (!isOpen) return null;

  const totalChats = new Set([...availableChats.map(c => c.number), currentChatNumber]).size;

  return (
    <div 
      className={`${styles.sidebar} ai-chat-font`}
      style={{ 
        width: `${sidebarWidth}px`,
        transform: isOpen ? 'translateX(0)' : 'translateX(100%)',
        transition: 'transform 0.3s ease-in-out'
      }}
    >
      {/* Resize Handle */}
      <div 
        ref={resizeHandleRef}
        className={styles['resize-handle']}
        onMouseDown={onResizeStart}
      />
      
      <div className={styles['sidebar-content']}>
        <div className="p-3 flex flex-col h-full">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-white flex items-center gap-2">
              <Bot size={24} className="text-noir-accent" />
              AI Research Assistant
            </h2>
            <div className="flex items-center gap-2">
              {/* Stop Generating Button */}
              {isLoading && abortController && (
                <button
                  onClick={stopGenerating}
                  className="text-red-600 hover:text-red-700 transition-colors text-sm bg-red-50 hover:bg-red-100 p-2 rounded-md"
                  title="Stop generating"
                >
                  <Square size={16} />
                </button>
              )}
              
              {/* Chat History Button */}
              {totalChats > 1 && (
                <div className="relative">
                  <button
                    onClick={() => setShowChatHistory(!showChatHistory)}
                    className="text-black/70 hover:text-black transition-colors text-sm bg-white/10 hover:bg-white/20 p-2 rounded-md"
                    title="Chat history"
                  >
                    <RotateCcw size={16} />
                  </button>
                  {showChatHistory && (
                    <div className="absolute right-0 top-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10 min-w-[200px]">
                      {availableChats.map((chat) => (
                        <button
                          key={chat.number}
                          onClick={() => switchToChat(chat.number)}
                          className={`w-full text-left px-3 py-2 hover:bg-gray-100 text-sm ${
                            currentChatNumber === chat.number ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <span>
                              {chat.number === 0 ? 'Initial Chat' : `Chat ${chat.number + 1}`}
                            </span>
                            <span className="text-xs text-gray-500">
                              {chat.messageCount} msgs
                            </span>
                          </div>
                          <div className="text-xs text-gray-400">
                            {chat.lastActivity.toLocaleDateString()}
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
              
              {/* New Chat Button */}
              <button
                onClick={startNewChat}
                className="text-black/70 hover:text-black transition-colors text-sm bg-white/10 hover:bg-white/20 p-2 rounded-md"
                title="Start new chat"
              >
                <Plus size={16} />
              </button>
              
              {/* Close Button */}
              <button
                onClick={onClose}
                className="text-black/70 hover:text-black transition-colors text-sm bg-white/10 hover:bg-white/20 p-2 rounded-md"
                title="Close chat"
              >
                <X size={16} />
              </button>
            </div>
          </div>
          
          {/* Welcome message when no messages exist */}
          {messages.length === 0 ? (
            <div className="flex-1 flex flex-col items-center justify-center text-center px-2">
              <Bot size={48} className="text-noir-accent mb-4" />
              <h3 className="text-xl font-semibold text-black mb-2">
                Ask AI to research any topic
              </h3>
              <p className="text-black/70 mb-6">
                The AI assistant will automatically research your topic and generate elements for your board.
              </p>
            </div>
          ) : (
            // Chat messages container
            <div className="flex-1 overflow-y-auto mb-4">
              <div className="space-y-3">
                {messages.map((message, index) => (
                  <div key={index} className={`${styles.comment} ${message.role === 'user' ? styles['user-message'] : styles['assistant-message']} ${index === messages.length - 1 && message.role === 'assistant' ? styles['new-comment'] : ''}`}>
                    {message.role === 'user' ? (
                      <>
                        <p className={styles['comment-username']}>You</p>
                        <p className={styles['comment-time']}>
                          {message.createdAt ? message.createdAt.toLocaleTimeString() : 'now'}
                        </p>
                        <p className={styles['comment-body']}>{message.content}</p>
                      </>
                    ) : (
                      <>
                        <p className={styles['comment-username']}>AI Assistant</p>
                        <p className={styles['comment-time']}>
                          {message.createdAt ? message.createdAt.toLocaleTimeString() : 'now'}
                        </p>
                        
                        {/* Show timeline items in chronological order */}
                        {message.timeline?.map((timelineItem, timelineIndex) => (
                          <div key={timelineIndex}>
                            {timelineItem.type === 'tool' && timelineItem.toolExecution && (
                              <ToolExecutionCard execution={timelineItem.toolExecution} />
                            )}
                            {timelineItem.type === 'text' && timelineItem.content && (
                              <div className={styles['comment-body']}>
                                {formatAIResponse(timelineItem.content)}
                              </div>
                            )}
                          </div>
                        ))}
                        
                        {/* Fallback: if no timeline, show legacy format */}
                        {!message.timeline && (
                          <>
                            {/* Show tool executions */}
                            {message.toolExecutions?.map((execution, execIndex) => (
                              <ToolExecutionCard key={execIndex} execution={execution} />
                            ))}
                            
                            <div className={styles['comment-body']}>
                              {formatAIResponse(message.content)}
                            </div>
                          </>
                        )}
                        
                        {/* Show thinking indicator when AI is thinking */}
                        {message.isThinking && message.thinkingStartTime ? (
                          <div className="flex items-center justify-start py-2">
                            <ThinkingIndicator startTime={message.thinkingStartTime} />
                          </div>
                        ) : null}

                        {/* Show typing indicator for streaming messages with no content yet (and not thinking) */}
                        {!message.isThinking && message.content.trim() === '' && !message.isComplete && (!message.timeline || message.timeline.length === 0) ? (
                          <div className="flex items-center justify-start py-2">
                            <TypingIndicator />
                          </div>
                        ) : null}
                        

                      </>
                    )}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>
          )}
          
          {/* Input area */}
          <div className="mt-auto">
            {/* Message Limit Display */} 
            <div className="text-xs text-center text-gray-800 mb-2 px-2">
              {resetTime ? (
                `Daily limit reached. Next message available in ${Math.ceil((resetTime.getTime() - Date.now()) / (1000 * 60 * 60))} hours.`
              ) : (
                `${remainingMessages}/${messageLimit} messages remaining today for this board.`
              )}
            </div>
            
            <AIChatInput
              onSubmit={handleInputSubmit}
              isLoading={isLoading}
              disabled={remainingMessages <= 0}
              contextItems={contextItems}
              onContextItemsChange={setContextItems}
              onFocusContext={handleFocusContext}
            />
          </div>
        </div>
      </div>
    </div>
  );
});

export default AIChatSidebar; 