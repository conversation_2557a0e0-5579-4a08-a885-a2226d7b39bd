# RFC: Backend-Authoritative AI Batch Persistence for Board Operations

Status: Draft  
Owner: Kilo Code  
Last updated: 2025-08-03

Problem Statement
Front-end currently persists AI tool results inline during streaming:
- Creates/connections are applied by the UI using AI-provided UUIDs
- Updates/deletes are returned by the tools API as instructions for the UI to execute
- There are dual sources of truth: the AI route maintains an in-memory cache while the UI persists to the DB via context-driven handlers
- Timing sensitivity leads to race conditions, especially for same-turn deletions and deletes by from/to endpoints

Goal
Move persistence to a backend, transactional batch that executes ordered operations for a single AI turn and returns authoritative results. The UI becomes a renderer of server-committed state, and the AI route cache mirrors server state.

Glossary
- Turn: A cohesive set of tool executions sent by the AI model within a single user prompt-response streaming cycle
- Operations: Create element, create connection, update element, delete element, delete connection
- Authoritative IDs: UUIDs that the server accepts (preferably provided by the tools) and stores as final

Non-goals
- Redesign of the board domain model
- Real-time collaboration protocol changes
- Changes to non-AI user-initiated board actions

High-level Design

Sequence overview
sequenceDiagram
  participant Client as Frontend
  participant AI as AI Route (/api/ai)
  participant Tools as Tools API (/api/ai/tools)
  participant DB as Database
  Client->>AI: Structured chat request (with board snapshot)
  AI->>AI: Execute tool calls, build ordered ops for the turn
  AI->>Tools: POST /api/ai/tools/batch-persist(ops, board_id, turn_id)
  Tools->>DB: Apply in a transaction (create, connect, update, delete)
  DB-->>Tools: Committed records
  Tools-->>AI: BatchPersistResult (authoritative records)
  AI-->>Client: Stream tool_update with authoritative results
  Client->>Client: Render state; no additional persistence calls

Key principles
- Single writer: The backend tools endpoint is the only code that mutates board data for AI-driven operations
- Transactional: All operations in a turn are applied in a single transaction; either all succeed or none
- Deterministic IDs: Prefer client-provided UUIDs from tools for creates so references are stable across the turn
- Ordered phases: Enforce operation ordering to avoid referential hazards:
  1) Create elements
  2) Create connections
  3) Update elements
  4) Delete connections
  5) Delete elements

API Specification

Endpoint
POST /api/ai/tools/batch-persist

Request body
{
  "turn_id": "uuid",
  "board_id": "uuid",
  "operations": [
    // Create elements
    {
      "op": "create_element",
      "id": "uuid",                // preferred: client-provided
      "type": "sticky-yellow|text|article|image|...",
      "position": { "x": 0, "y": 0 },
      "props": {
        "content": "string",
        "title": "string",
        "url": "string",
        "file_url": "string",
        "website_url": "string",
        "imageUrl": "string",
        "alt": "string",
        "...": "other per-type props"
      }
    },
    // Create connections
    {
      "op": "create_connection",
      "id": "uuid",                // preferred: client-provided
      "from_id": "uuid",
      "to_id": "uuid",
      "label": "string"
    },
    // Update elements
    {
      "op": "update_element",
      "element_id": "uuid",
      "updates": {
        "content": "...",
        "title": "...",
        "position": { "x": 10, "y": 20 },
        "width": 240,
        "height": 120,
        "file_url": "string",
        "website_url": "string",
        "imageUrl": "string",
        "type": "image|image-invisible|..."
      }
    },
    // Delete connections
    { "op": "delete_connection", "connection_id": "uuid" },
    { "op": "delete_connection_by_endpoints", "from_id": "uuid", "to_id": "uuid" },
    // Delete elements
    { "op": "delete_element", "element_id": "uuid" }
  ]
}

Response 200
{
  "turn_id": "uuid",
  "board_id": "uuid",
  "results": {
    "created_elements": [
      {
        "id": "uuid",
        "type": "sticky-yellow",
        "position": { "x": 0, "y": 0 },
        "width": 200,
        "height": 200,
        "content": "...",
        "title": "...",
        "url": "...",
        "file_url": "...",
        "website_url": "...",
        "imageUrl": "...",
        "alt": "..."
      }
    ],
    "created_connections": [
      { "id": "uuid", "from_id": "uuid", "to_id": "uuid", "label": "..." }
    ],
    "updated_elements": [
      { "id": "uuid", "updates": { "width": 220, "height": 160, "...": "..." } }
    ],
    "deleted_element_ids": ["uuid", "..."],
    "deleted_connection_ids": ["uuid", "..."],
    "warnings": [
      { "code": "CONNECTION_EXISTS", "message": "Connection from A to B already existed; skipped" }
    ]
  }
}

Response 207 (Multi-status with partial warnings, but still committed)
{
  "turn_id": "uuid",
  "board_id": "uuid",
  "results": { ... },
  "warnings": [ ... ]
}

Response 400/422
- Invalid schema, unknown op, missing ids, referential integrity issues

Response 409
- Conflict with board state that prevents transaction (e.g., board locked or version conflict)

Response 500
- Internal error; nothing committed

Validation and Ordering Rules

Accepted operations
- create_element: requires id, type, position; props are type-specific
- create_connection: requires id, from_id, to_id; must reference existing or in-turn-created element ids
- update_element: requires element_id and non-empty updates object
- delete_connection: requires connection_id
- delete_connection_by_endpoints: requires from_id and to_id
- delete_element: requires element_id

Server enforced ordering
1. create_element
2. create_connection
3. update_element
4. delete_connection/delete_connection_by_endpoints
5. delete_element

Within each category, operations are applied in input order.

Conflict handling
- If create_connection references unknown from_id/to_id not present in DB or earlier in this batch, return 422
- If update_element targets a missing element and that element was deleted in this same batch, return 422
- delete_connection_by_endpoints: if connection does not exist, emit a warning and continue
- delete_element automatically cascades to delete any associated connections (recommended server behavior) or explicitly requires clients to include delete_connection ops first. Choose one:
  - Preferred: Server cascades connection deletions to ensure integrity and simplify clients

Transaction Strategy
- Start transaction
- Apply ordered operations
- If any operation fails hard (not a permissible warning), rollback and return error
- If all succeed or succeed with warnings, commit and return results/warnings

AI Route Integration

Where to collect and commit
- In [`ai.processStructuredConversation()`](src/app/api/ai/route.ts:1), accumulate tool results for the current turn:
  - Convert create/update/delete/connection tool outputs into canonical batch operations as defined above
- On end-of-turn boundary (already detectable in structured streaming), call:
  - POST /api/ai/tools/batch-persist with board_id, turn_id, operations
- Stream tool_update events using the authoritative batch result:
  - content type: "tool_update"
  - payload: the committed results, suitable for UI to render without further persistence
- Update the in-memory boardStateCache to mirror the committed results instead of being authoritative. Cache is now an optimization, not a source of truth.

Frontend Integration

AIChatSidebar.tsx
- Stop directly calling onCreateElements/onProcessAIOperations during streaming for AI-driven ops
- Instead, listen for tool_update events that carry authoritative batch results and update the UI with those results via a single renderer method, e.g. applyBatchResults
- Optionally support optimistic rendering, but reconcile against final batch results when received (IDs should remain stable if server accepts client-provided UUIDs)

DetectiveBoard.tsx
- Add a renderer function applyBatchResults(results):
  - Merge created_elements and created_connections into BoardContext state
  - Apply updated_elements
  - Remove deleted ids (elements, connections)
- No timing hacks or waits necessary; follow server output order

Data Model Notes

IDs
- Prefer client-provided UUIDs for elements and connections to maintain in-turn references
- Server validates UUID format and rejects duplicates on the same board
- If the server must override IDs (discouraged), it must return id_map to reconcile; otherwise stick to stable IDs

Cascading deletes
- Server should cascade delete connections when an element is deleted in a batch
- This simplifies clients and prevents endpoint resolution checks for connection deletes when elements are deleted in the same turn

Schema examples

Create element (article)
{
  "op": "create_element",
  "id": "c141c64c-2ed8-4d7b-8bfa-3b60a0e3aef1",
  "type": "article",
  "position": { "x": 300, "y": 420 },
  "props": {
    "title": "Sample Article",
    "url": "https://example.com",
    "website_url": "https://example.com",
    "file_url": "/storage/articles/abc123.png",
    "imageUrl": "https://example.com/abc123.png"
  }
}

Create connection
{
  "op": "create_connection",
  "id": "0b2a3d49-1447-4d24-9b9e-7e3cb5a7d890",
  "from_id": "c141c64c-2ed8-4d7b-8bfa-3b60a0e3aef1",
  "to_id": "d2c0fb43-8d2f-4b8d-8db7-7a2b2d3a1e42",
  "label": "relates-to"
}

Delete connection by endpoints
{
  "op": "delete_connection_by_endpoints",
  "from_id": "c141c64c-2ed8-4d7b-8bfa-3b60a0e3aef1",
  "to_id": "d2c0fb43-8d2f-4b8d-8db7-7a2b2d3a1e42"
}

Edge Cases and Policies

Idempotency
- If the same operation id is resent within the same turn (rare in structured streaming), the server should ignore duplicates
- For create operations with existing IDs, treat as conflict (409) unless the payload is identical; to simplify, recommend the AI route filters duplicates before sending

Partial failures
- Prefer all-or-nothing within a turn; if recoverable warnings occur (e.g., deleting a non-existent connection by endpoints), commit and return warnings

Large boards and lazy loading
- Batch results should include all created IDs so the UI can immediately treat them as visible, avoiding lazy-loading hiding just-created items

Security and Access Control
- Authenticate the user
- Authorize that the user has write access to board_id
- Validate turn_id format (uuid) for traceability
- Rate limit to prevent abuse

Migration Plan

Phase 1: Add endpoint and server-side batch implementation
- Implement POST /api/ai/tools/batch-persist with transaction
- Add internal service methods to apply ops to existing board schema (reuse current element/connection persistence code)

Phase 2: AI route integration
- Accumulate operations per turn
- On end-of-turn, call batch-persist
- Stream authoritative results back to the client

Phase 3: Frontend switchover
- Update AIChatSidebar to stop direct persistence; instead render applyBatchResults
- Implement applyBatchResults in DetectiveBoard using BoardContext mutation methods

Phase 4: Cleanup and deprecation
- Remove timing hacks (delays, aiCreatedElementIds trackers) that were compensating for races
- Simplify ai/route.ts boardStateCache to mirror server-confirmed state only

Testing Strategy

Unit
- Validate operation ordering and transactional behavior
- Validate cascade delete policy
- Validate error and warning paths (e.g., delete by endpoints that doesn’t exist)

Integration
- Same-turn create+connect
- Same-turn create+connect+delete element (ensure connection deletion via cascade)
- Same-turn connect then delete connection by id and by endpoints
- Mixed: reference a pre-existing element and a newly created one in the same turn

E2E
- Stream structured response with tool calls; verify UI reflects final server results and no local persistence calls were made by the client

Open Questions
- Should the server strictly require client-provided UUIDs for creates? Recommendation: yes, to keep references stable
- Should we keep an optional optimistic UI mode while awaiting commit? Recommendation: optional; if used, must reconcile with authoritative results

Appendix A: Integration Pointers (Files)

Server
- Tools endpoint addition: [`tools.batchPersist()`](src/app/api/ai/tools/route.ts:1)
- AI route integration point: [`ai.processStructuredConversation()`](src/app/api/ai/route.ts:1)

Client
- Replace inline persistence in [`AIChatSidebar.tsx`](src/components/features/detective/AIChatSidebar.tsx:1) with render-only handling of tool_update batch results
- Add applyBatchResults helper in [`DetectiveBoard.tsx`](src/components/features/detective/DetectiveBoard.tsx:1)

Success Criteria
- No flakiness for same-turn deletes or endpoint-based connection deletes
- Single authoritative source of persisted state (backend)
- Stable IDs across an AI turn
- Reduced reliance on frontend timing workarounds

For reference below is hte manage_element rpc function in the database

DECLARE
    v_element_id UUID;
    result_element RECORD;
    has_permission BOOLEAN;
    formatted_result JSONB;
    v_owner_id uuid;
BEGIN
    -- Verify permission first
    SELECT user_id
        INTO v_owner_id
        FROM public.boards
    WHERE id = p_board_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Board % does not exist', p_board_id;
    END IF;

    -- 2) If caller isn’t owner, check the can_edit_board helper
    IF v_owner_id <> p_user_id THEN
        IF NOT has_board_edit_permission(p_board_id, p_user_id) THEN
        RAISE EXCEPTION 'User % does not have permission to edit board %',
                        p_user_id, p_board_id;
        END IF;
    END IF;

    -- Always update board timestamp
    UPDATE boards SET updated_at = now() WHERE id = p_board_id;

    -- Perform action
    CASE p_action
        WHEN 'add' THEN
            IF p_element->>'type' IS NULL OR p_element->'position' IS NULL THEN
                RAISE EXCEPTION 'Element type and position are required for add action.';
            END IF;

            INSERT INTO elements (
                id,
                board_id,
                element_type,
                title,
                text_content,
                file_url,
                website_url,
                position_x,
                position_y,
                width,
                height,
                is_ai_generated
            )
            VALUES (
                COALESCE(
                NULLIF(p_element->>'id','')::uuid,
                gen_random_uuid()
                ),
                p_board_id,
                p_element->>'type',
                COALESCE(p_element->>'title', p_element->>'type'),
                p_element->>'content',
                COALESCE(p_element->>'file_url', p_element->>'url'),
                CASE
                    WHEN p_element->>'type' = 'article'
                    THEN COALESCE(p_element->>'website_url', p_element->>'url')
                    ELSE NULL
                END,
                (p_element->'position'->>'x')::numeric,
                (p_element->'position'->>'y')::numeric,
                (p_element->>'width')::numeric,
                (p_element->>'height')::numeric,
                COALESCE((p_element->>'isAiGenerated')::boolean, false)
            )
        RETURNING * INTO result_element;

        WHEN 'update' THEN
            IF p_element_id IS NULL AND p_element->>'id' IS NULL THEN
                 RAISE EXCEPTION 'Element ID is required for update action.';
            END IF;

            v_element_id := COALESCE(p_element_id, (p_element->>'id')::uuid);

            -- Debugging: Check for existence before updating to isolate RLS issues.
            PERFORM 1 FROM elements WHERE id = v_element_id AND board_id = p_board_id;
            IF NOT FOUND THEN
                RAISE EXCEPTION 'DEBUG: Element % not found on board % right before update. This indicates an issue with the RLS USING clause.', v_element_id, p_board_id;
            END IF;

            UPDATE elements
            SET
                element_type = COALESCE(p_element->>'type', element_type),
                title        = COALESCE(p_element->>'title', title),
                text_content = COALESCE(p_element->>'content', text_content),
                file_url     = COALESCE(p_element->>'file_url', p_element->>'url', file_url),
                website_url  = CASE
                                 WHEN COALESCE(p_element->>'type', element_type) = 'article'
                                 THEN COALESCE(p_element->>'website_url', p_element->>'url', website_url)
                                 ELSE website_url
                               END,
                position_x   = COALESCE((p_element->'position'->>'x')::numeric, position_x),
                position_y   = COALESCE((p_element->'position'->>'y')::numeric, position_y),
                width        = COALESCE((p_element->>'width')::numeric, width),
                height       = COALESCE((p_element->>'height')::numeric, height),
                updated_at   = now()
            WHERE id = v_element_id
              AND board_id = p_board_id
            RETURNING * INTO result_element;

            IF result_element IS NULL THEN
                RAISE EXCEPTION 'Element % not found on board % or update failed. This likely means the RLS WITH CHECK clause prevented the update.',
                                 v_element_id, p_board_id;
            END IF;

        WHEN 'delete' THEN
            -- Delete action remains unchanged
            IF p_element_id IS NULL AND p_element->>'id' IS NULL THEN
                 RAISE EXCEPTION 'Element ID is required for delete action.';
            END IF;
            v_element_id := COALESCE(p_element_id, (p_element->>'id')::uuid);

            DECLARE
                v_file_path TEXT;
                v_elem_type TEXT;
                result_payload JSONB; -- Variable to build the return payload
            BEGIN
                -- 1. Find the element and get its type and file path BEFORE deleting the row
                SELECT element_type, file_url
                INTO v_elem_type, v_file_path
                FROM elements
                WHERE id = v_element_id AND board_id = p_board_id;

                -- Check if element was found
                IF NOT FOUND THEN
                     RAISE EXCEPTION 'Element % not found on board %.', v_element_id, p_board_id;
                END IF;

                -- 2. Delete the element record from the database
                DELETE FROM elements
                WHERE id = v_element_id AND board_id = p_board_id;

                -- 3. Construct the return payload
                result_payload := jsonb_build_object('message', 'Element deleted successfully');

                -- 4. If it had a file path (image or article), add it to the payload
                IF v_file_path IS NOT NULL AND v_file_path <> '' THEN
                    result_payload := result_payload || jsonb_build_object('deleted_file_path', v_file_path);
                    RAISE LOG 'Element deleted with file path, returning for API deletion: %', v_file_path;
                END IF;

                RETURN result_payload; -- Return the constructed payload
            END; -- End of inner block

        ELSE
            RAISE EXCEPTION 'Invalid action specified: %', p_action;
    END CASE;

    -- Format the result to match frontend expectations
    formatted_result := jsonb_build_object(
        'id',       result_element.id,
        'type',     result_element.element_type,
        'title',    result_element.title,
        'content',  COALESCE(result_element.text_content, ''),
        'url',      COALESCE(result_element.file_url, ''),
        'position', jsonb_build_object('x', result_element.position_x, 'y', result_element.position_y),
        'width',    result_element.width,
        'height',   result_element.height,
        'isAiGenerated', result_element.is_ai_generated
    );

    -- For elements with file_url, add it to the response
    IF result_element.file_url IS NOT NULL THEN
        formatted_result := formatted_result || jsonb_build_object('file_url', result_element.file_url);
    END IF;

    -- For image elements, add the imageUrl property set to file_url
    IF result_element.element_type = 'image' THEN
        formatted_result := formatted_result || jsonb_build_object('imageUrl', result_element.file_url);
    END IF;

    -- For article elements, add website_url to the result
    IF result_element.element_type = 'article' THEN
        formatted_result := formatted_result || jsonb_build_object('website_url', result_element.website_url);
    END IF;

    RETURN formatted_result;

EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        RAISE WARNING 'Error in manage_element: %', SQLERRM;
        -- Re-raise the exception to ensure transaction rollback
        RAISE;
END;


And the manage connection function
DECLARE
    v_connection_id UUID;
    v_from_element_id UUID;
    v_to_element_id UUID;
    result_connection RECORD;
    has_permission BOOLEAN;
    formatted_result JSONB;
    v_owner_id uuid; -- Added for owner check
BEGIN
    -- 1) Verify permission: Check if the user owns the board first
    SELECT user_id
        INTO v_owner_id
        FROM public.boards
    WHERE id = p_board_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Board % does not exist', p_board_id;
    END IF;

    -- 2) If caller isn’t owner, check the can_edit_board helper
    IF v_owner_id <> p_user_id THEN
        IF NOT has_board_edit_permission(p_board_id, p_user_id) THEN
        RAISE EXCEPTION 'User % does not have permission to edit board %',
                        p_user_id, p_board_id;
        END IF;
    END IF;

    -- Always update board timestamp (as it's an edit operation)
    UPDATE boards SET updated_at = now() WHERE id = p_board_id;

    -- Perform action
    CASE p_action
      WHEN 'add' THEN
            -- Validate required fields for add
            IF p_connection->>'fromId' IS NULL OR p_connection->>'toId' IS NULL THEN
                RAISE EXCEPTION 'From element ID and to element ID are required for add action.';
            END IF;
            v_from_element_id := (p_connection->>'fromId')::uuid;
            v_to_element_id   := (p_connection->>'toId')::uuid;

            -- Optional: Verify elements exist on this board before adding connection
            IF NOT EXISTS (
                SELECT 1 FROM elements
                WHERE id = v_from_element_id AND board_id = p_board_id
            )
            OR NOT EXISTS (
                SELECT 1 FROM elements
                WHERE id = v_to_element_id   AND board_id = p_board_id
            )
            THEN
                RAISE EXCEPTION 'One or both elements not found on this board.';
            END IF;

            INSERT INTO connections (
                id,               -- allow client to supply id
                board_id,
                from_element_id,
                to_element_id,
                connection_type,
                label,
                is_ai_generated
            )
            VALUES (
                COALESCE(
                NULLIF(p_connection->>'id','')::uuid,
                gen_random_uuid()
                ),
                p_board_id,
                v_from_element_id,
                v_to_element_id,
                COALESCE(p_connection->>'type', 'default'),
                p_connection->>'label',
                COALESCE((p_connection->>'isAiGenerated')::boolean, false)
            )
        RETURNING * INTO result_connection;


        WHEN 'update' THEN
            IF p_connection->>'id' IS NULL THEN
                 RAISE EXCEPTION 'Connection ID is required for update action.';
            END IF;
            v_connection_id := (p_connection->>'id')::uuid;

            UPDATE connections
            SET
                connection_type = COALESCE(p_connection->>'type', connection_type),
                label = COALESCE(p_connection->>'label', label),
                -- from/to elements, is_ai_generated usually not updated
                updated_at = now()
            WHERE id = v_connection_id AND board_id = p_board_id -- Ensure it's the right board implicitly via FK/RLS might be better
            RETURNING * INTO result_connection;

            IF result_connection IS NULL THEN
                RAISE EXCEPTION 'Connection % not found on board % or update failed.', v_connection_id, p_board_id;
            END IF;

        WHEN 'delete' THEN
            IF p_connection->>'id' IS NULL THEN
                 RAISE EXCEPTION 'Connection ID is required for delete action.';
            END IF;
            v_connection_id := (p_connection->>'id')::uuid;

            DELETE FROM connections
            WHERE id = v_connection_id AND board_id = p_board_id
            RETURNING id INTO v_connection_id; -- Check if delete was successful

            IF v_connection_id IS NULL THEN
                 RAISE EXCEPTION 'Connection % not found on board % or delete failed.', (p_connection->>'id')::uuid, p_board_id;
            END IF;
            RETURN jsonb_build_object('message', 'Connection deleted successfully');

        ELSE
            RAISE EXCEPTION 'Invalid action specified: %', p_action;
    END CASE;

    -- Format the result to match frontend expectations
    formatted_result := jsonb_build_object(
        'id', result_connection.id,
        'fromId', result_connection.from_element_id,
        'toId', result_connection.to_element_id,
        'type', result_connection.connection_type,
        'label', COALESCE(result_connection.label, ''),
        'isAiGenerated', result_connection.is_ai_generated
    );

    RETURN formatted_result;

EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        RAISE WARNING 'Error in manage_connection: %', SQLERRM;
        -- Re-raise the exception to ensure transaction rollback
        RAISE;
END;
