import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

// Types for batch operations
interface BatchOperation {
  op: 'create_element' | 'create_connection' | 'update_element' | 'delete_connection' | 'delete_connection_by_endpoints' | 'delete_element';
  [key: string]: any;
}

interface CreateElementOperation extends BatchOperation {
  op: 'create_element';
  id: string;
  type: string;
  position: { x: number; y: number };
  props: {
    content?: string;
    title?: string;
    url?: string;
    file_url?: string;
    website_url?: string;
    imageUrl?: string;
    alt?: string;
    [key: string]: any;
  };
}

interface CreateConnectionOperation extends BatchOperation {
  op: 'create_connection';
  id: string;
  from_id: string;
  to_id: string;
  label?: string;
}

interface UpdateElementOperation extends BatchOperation {
  op: 'update_element';
  element_id: string;
  updates: {
    content?: string;
    title?: string;
    position?: { x: number; y: number };
    width?: number;
    height?: number;
    file_url?: string;
    website_url?: string;
    imageUrl?: string;
    type?: string;
    [key: string]: any;
  };
}

interface DeleteConnectionOperation extends BatchOperation {
  op: 'delete_connection';
  connection_id: string;
}

interface DeleteConnectionByEndpointsOperation extends BatchOperation {
  op: 'delete_connection_by_endpoints';
  from_id: string;
  to_id: string;
}

interface DeleteElementOperation extends BatchOperation {
  op: 'delete_element';
  element_id: string;
}

interface BatchPersistRequest {
  turn_id: string;
  board_id: string;
  operations: BatchOperation[];
}

interface BatchPersistResponse {
  turn_id: string;
  board_id: string;
  results: {
    created_elements: any[];
    created_connections: any[];
    updated_elements: any[];
    deleted_element_ids: string[];
    deleted_connection_ids: string[];
    warnings: Array<{ code: string; message: string }>;
  };
}

// Operation ordering phases
const OPERATION_PHASES = {
  CREATE_ELEMENT: 1,
  CREATE_CONNECTION: 2,
  UPDATE_ELEMENT: 3,
  DELETE_CONNECTION: 4,
  DELETE_ELEMENT: 5,
} as const;

function getOperationPhase(op: string): number {
  switch (op) {
    case 'create_element':
      return OPERATION_PHASES.CREATE_ELEMENT;
    case 'create_connection':
      return OPERATION_PHASES.CREATE_CONNECTION;
    case 'update_element':
      return OPERATION_PHASES.UPDATE_ELEMENT;
    case 'delete_connection':
    case 'delete_connection_by_endpoints':
      return OPERATION_PHASES.DELETE_CONNECTION;
    case 'delete_element':
      return OPERATION_PHASES.DELETE_ELEMENT;
    default:
      throw new Error(`Unknown operation: ${op}`);
  }
}

// Validation functions
function validateBatchRequest(request: any): BatchPersistRequest {
  if (!request.turn_id || typeof request.turn_id !== 'string') {
    throw new Error('Invalid turn_id: must be a non-empty string');
  }
  
  if (!request.board_id || typeof request.board_id !== 'string') {
    throw new Error('Invalid board_id: must be a non-empty string');
  }
  
  if (!Array.isArray(request.operations)) {
    throw new Error('Invalid operations: must be an array');
  }
  
  // Validate each operation
  for (const operation of request.operations) {
    validateOperation(operation);
  }
  
  return request as BatchPersistRequest;
}

function validateOperation(operation: any): void {
  if (!operation.op || typeof operation.op !== 'string') {
    throw new Error('Invalid operation: missing or invalid "op" field');
  }
  
  switch (operation.op) {
    case 'create_element':
      validateCreateElementOperation(operation);
      break;
    case 'create_connection':
      validateCreateConnectionOperation(operation);
      break;
    case 'update_element':
      validateUpdateElementOperation(operation);
      break;
    case 'delete_connection':
      validateDeleteConnectionOperation(operation);
      break;
    case 'delete_connection_by_endpoints':
      validateDeleteConnectionByEndpointsOperation(operation);
      break;
    case 'delete_element':
      validateDeleteElementOperation(operation);
      break;
    default:
      throw new Error(`Unknown operation type: ${operation.op}`);
  }
}

function validateCreateElementOperation(op: any): void {
  if (!op.id || typeof op.id !== 'string') {
    throw new Error('create_element: missing or invalid id');
  }
  if (!op.type || typeof op.type !== 'string') {
    throw new Error('create_element: missing or invalid type');
  }
  if (!op.position || typeof op.position.x !== 'number' || typeof op.position.y !== 'number') {
    throw new Error('create_element: missing or invalid position');
  }
  if (!op.props || typeof op.props !== 'object') {
    throw new Error('create_element: missing or invalid props');
  }
}

function validateCreateConnectionOperation(op: any): void {
  if (!op.id || typeof op.id !== 'string') {
    throw new Error('create_connection: missing or invalid id');
  }
  if (!op.from_id || typeof op.from_id !== 'string') {
    throw new Error('create_connection: missing or invalid from_id');
  }
  if (!op.to_id || typeof op.to_id !== 'string') {
    throw new Error('create_connection: missing or invalid to_id');
  }
}

function validateUpdateElementOperation(op: any): void {
  if (!op.element_id || typeof op.element_id !== 'string') {
    throw new Error('update_element: missing or invalid element_id');
  }
  if (!op.updates || typeof op.updates !== 'object' || Object.keys(op.updates).length === 0) {
    throw new Error('update_element: missing or invalid updates object');
  }
}

function validateDeleteConnectionOperation(op: any): void {
  if (!op.connection_id || typeof op.connection_id !== 'string') {
    throw new Error('delete_connection: missing or invalid connection_id');
  }
}

function validateDeleteConnectionByEndpointsOperation(op: any): void {
  if (!op.from_id || typeof op.from_id !== 'string') {
    throw new Error('delete_connection_by_endpoints: missing or invalid from_id');
  }
  if (!op.to_id || typeof op.to_id !== 'string') {
    throw new Error('delete_connection_by_endpoints: missing or invalid to_id');
  }
}

function validateDeleteElementOperation(op: any): void {
  if (!op.element_id || typeof op.element_id !== 'string') {
    throw new Error('delete_element: missing or invalid element_id');
  }
}

// Authentication helper
async function getAuthenticatedUser(request: NextRequest) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
  
  const supabase = createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        Authorization: request.headers.get('Authorization') || '',
      },
    },
  });

  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    throw new Error('Authentication required');
  }
  
  return { user, supabase };
}

// Sort operations by phase and maintain order within each phase
function sortOperations(operations: BatchOperation[]): BatchOperation[] {
  return operations.sort((a, b) => {
    const phaseA = getOperationPhase(a.op);
    const phaseB = getOperationPhase(b.op);
    return phaseA - phaseB;
  });
}

// Process operations in a transaction
async function processBatchOperations(
  supabase: any,
  userId: string,
  boardId: string,
  operations: BatchOperation[]
): Promise<BatchPersistResponse['results']> {
  const results: BatchPersistResponse['results'] = {
    created_elements: [],
    created_connections: [],
    updated_elements: [],
    deleted_element_ids: [],
    deleted_connection_ids: [],
    warnings: [],
  };

  // Sort operations by phase
  const sortedOperations = sortOperations(operations);

  console.log(`[Batch Persist] Processing operations in order:`, sortedOperations.map(op => op.op));

  // Process each operation
  for (const operation of sortedOperations) {
    try {
      switch (operation.op) {
        case 'create_element':
          await processCreateElement(supabase, userId, boardId, operation as CreateElementOperation, results);
          break;
        case 'create_connection':
          await processCreateConnection(supabase, userId, boardId, operation as CreateConnectionOperation, results);
          break;
        case 'update_element':
          await processUpdateElement(supabase, userId, boardId, operation as UpdateElementOperation, results);
          break;
        case 'delete_connection':
          await processDeleteConnection(supabase, userId, boardId, operation as DeleteConnectionOperation, results);
          break;
        case 'delete_connection_by_endpoints':
          await processDeleteConnectionByEndpoints(supabase, userId, boardId, operation as DeleteConnectionByEndpointsOperation, results);
          break;
        case 'delete_element':
          await processDeleteElement(supabase, userId, boardId, operation as DeleteElementOperation, results);
          break;
        default:
          throw new Error(`Unknown operation: ${operation.op}`);
      }
    } catch (error: any) {
      console.error(`[Batch Persist] Error processing operation ${operation.op}:`, error);
      throw error; // Re-throw to trigger transaction rollback
    }
  }

  return results;
}

async function processCreateElement(
  supabase: any,
  userId: string,
  boardId: string,
  operation: CreateElementOperation,
  results: BatchPersistResponse['results']
): Promise<void> {
  console.log(`[Batch Persist] Creating element ${operation.id} of type ${operation.type}`);

  // Prepare element data for manage_element RPC
  const elementData = {
    id: operation.id,
    type: operation.type,
    position: operation.position,
    content: operation.props.content || '',
    title: operation.props.title || operation.type,
    url: operation.props.url || operation.props.file_url || '',
    file_url: operation.props.file_url || operation.props.url || '',
    website_url: operation.props.website_url || (operation.type === 'article' ? operation.props.url : ''),
    width: operation.props.width || null,
    height: operation.props.height || null,
    isAiGenerated: true,
  };

  const { data, error } = await supabase.rpc('manage_element', {
    p_user_id: userId,
    p_board_id: boardId,
    p_action: 'add',
    p_element: elementData,
  });

  if (error) {
    throw new Error(`Failed to create element ${operation.id}: ${error.message}`);
  }

  results.created_elements.push(data);
  console.log(`[Batch Persist] Successfully created element ${operation.id}`);
}

async function processCreateConnection(
  supabase: any,
  userId: string,
  boardId: string,
  operation: CreateConnectionOperation,
  results: BatchPersistResponse['results']
): Promise<void> {
  console.log(`[Batch Persist] Creating connection ${operation.id}: ${operation.from_id} -> ${operation.to_id}`);

  // Prepare connection data for manage_connection RPC
  const connectionData = {
    id: operation.id,
    fromId: operation.from_id,
    toId: operation.to_id,
    label: operation.label || '',
    type: 'default',
    isAiGenerated: true,
  };

  const { data, error } = await supabase.rpc('manage_connection', {
    p_user_id: userId,
    p_board_id: boardId,
    p_action: 'add',
    p_connection: connectionData,
  });

  if (error) {
    throw new Error(`Failed to create connection ${operation.id}: ${error.message}`);
  }

  results.created_connections.push(data);
  console.log(`[Batch Persist] Successfully created connection ${operation.id}`);
}

async function processUpdateElement(
  supabase: any,
  userId: string,
  boardId: string,
  operation: UpdateElementOperation,
  results: BatchPersistResponse['results']
): Promise<void> {
  console.log(`[Batch Persist] Updating element ${operation.element_id}`);

  // Prepare update data for manage_element RPC
  const updateData = {
    id: operation.element_id,
    ...operation.updates,
  };

  const { data, error } = await supabase.rpc('manage_element', {
    p_user_id: userId,
    p_board_id: boardId,
    p_action: 'update',
    p_element: updateData,
  });

  if (error) {
    throw new Error(`Failed to update element ${operation.element_id}: ${error.message}`);
  }

  results.updated_elements.push({ id: operation.element_id, updates: operation.updates });
  console.log(`[Batch Persist] Successfully updated element ${operation.element_id}`);
}

async function processDeleteConnection(
  supabase: any,
  userId: string,
  boardId: string,
  operation: DeleteConnectionOperation,
  results: BatchPersistResponse['results']
): Promise<void> {
  console.log(`[Batch Persist] Deleting connection ${operation.connection_id}`);

  const connectionData = {
    id: operation.connection_id,
  };

  const { data, error } = await supabase.rpc('manage_connection', {
    p_user_id: userId,
    p_board_id: boardId,
    p_action: 'delete',
    p_connection: connectionData,
  });

  if (error) {
    throw new Error(`Failed to delete connection ${operation.connection_id}: ${error.message}`);
  }

  results.deleted_connection_ids.push(operation.connection_id);
  console.log(`[Batch Persist] Successfully deleted connection ${operation.connection_id}`);
}

async function processDeleteConnectionByEndpoints(
  supabase: any,
  userId: string,
  boardId: string,
  operation: DeleteConnectionByEndpointsOperation,
  results: BatchPersistResponse['results']
): Promise<void> {
  console.log(`[Batch Persist] Deleting connection by endpoints: ${operation.from_id} -> ${operation.to_id}`);

  // First, find the connection by endpoints
  const { data: connections, error: findError } = await supabase
    .from('connections')
    .select('id')
    .eq('board_id', boardId)
    .eq('from_element_id', operation.from_id)
    .eq('to_element_id', operation.to_id);

  if (findError) {
    throw new Error(`Failed to find connection: ${findError.message}`);
  }

  if (!connections || connections.length === 0) {
    // Connection doesn't exist - add warning but continue
    results.warnings.push({
      code: 'CONNECTION_NOT_FOUND',
      message: `Connection from ${operation.from_id} to ${operation.to_id} not found; skipped`,
    });
    console.log(`[Batch Persist] Connection not found: ${operation.from_id} -> ${operation.to_id}, adding warning`);
    return;
  }

  // Delete the found connection(s)
  for (const connection of connections) {
    const connectionData = {
      id: connection.id,
    };

    const { error } = await supabase.rpc('manage_connection', {
      p_user_id: userId,
      p_board_id: boardId,
      p_action: 'delete',
      p_connection: connectionData,
    });

    if (error) {
      throw new Error(`Failed to delete connection ${connection.id}: ${error.message}`);
    }

    results.deleted_connection_ids.push(connection.id);
    console.log(`[Batch Persist] Successfully deleted connection ${connection.id} by endpoints`);
  }
}

async function processDeleteElement(
  supabase: any,
  userId: string,
  boardId: string,
  operation: DeleteElementOperation,
  results: BatchPersistResponse['results']
): Promise<void> {
  console.log(`[Batch Persist] Deleting element ${operation.element_id}`);

  // First, find and delete associated connections (cascade delete)
  const { data: connections, error: findError } = await supabase
    .from('connections')
    .select('id')
    .eq('board_id', boardId)
    .or(`from_element_id.eq.${operation.element_id},to_element_id.eq.${operation.element_id}`);

  if (findError) {
    console.warn(`[Batch Persist] Warning: Could not find connections for element ${operation.element_id}: ${findError.message}`);
  } else if (connections && connections.length > 0) {
    console.log(`[Batch Persist] Found ${connections.length} connections to cascade delete for element ${operation.element_id}`);

    // Delete associated connections
    for (const connection of connections) {
      try {
        const { error } = await supabase.rpc('manage_connection', {
          p_user_id: userId,
          p_board_id: boardId,
          p_action: 'delete',
          p_connection: { id: connection.id },
        });

        if (error) {
          console.warn(`[Batch Persist] Warning: Failed to cascade delete connection ${connection.id}: ${error.message}`);
        } else {
          results.deleted_connection_ids.push(connection.id);
          console.log(`[Batch Persist] Cascade deleted connection ${connection.id}`);
        }
      } catch (error) {
        console.warn(`[Batch Persist] Warning: Error cascade deleting connection ${connection.id}:`, error);
      }
    }
  }

  // Delete the element
  const elementData = {
    id: operation.element_id,
  };

  const { data, error } = await supabase.rpc('manage_element', {
    p_user_id: userId,
    p_board_id: boardId,
    p_action: 'delete',
    p_element: elementData,
  });

  if (error) {
    throw new Error(`Failed to delete element ${operation.element_id}: ${error.message}`);
  }

  results.deleted_element_ids.push(operation.element_id);
  console.log(`[Batch Persist] Successfully deleted element ${operation.element_id}`);
}

export async function POST(request: NextRequest) {
  try {
    console.log('[Batch Persist] Starting batch persist request');

    // Parse and validate request
    const body = await request.json();
    const batchRequest = validateBatchRequest(body);

    console.log(`[Batch Persist] Processing ${batchRequest.operations.length} operations for turn ${batchRequest.turn_id}`);

    // Authenticate user
    const { user, supabase } = await getAuthenticatedUser(request);

    // Process operations in a transaction
    const results = await processBatchOperations(
      supabase,
      user.id,
      batchRequest.board_id,
      batchRequest.operations
    );

    const response: BatchPersistResponse = {
      turn_id: batchRequest.turn_id,
      board_id: batchRequest.board_id,
      results,
    };

    console.log(`[Batch Persist] Successfully processed batch: ${results.created_elements.length} elements created, ${results.created_connections.length} connections created, ${results.updated_elements.length} elements updated, ${results.deleted_element_ids.length} elements deleted, ${results.deleted_connection_ids.length} connections deleted, ${results.warnings.length} warnings`);

    return NextResponse.json(response);

  } catch (error: any) {
    console.error('[Batch Persist] Error:', error);

    if (error.message === 'Authentication required') {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    if (error.message.includes('Invalid') || error.message.includes('missing')) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error.message.includes('does not have permission')) {
      return NextResponse.json({ error: 'Permission denied to edit this board' }, { status: 403 });
    }

    if (error.message.includes('not found')) {
      return NextResponse.json({ error: 'Board or resource not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
