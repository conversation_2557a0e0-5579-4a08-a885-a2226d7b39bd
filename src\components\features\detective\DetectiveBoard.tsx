import React, { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { ArrowLeft, Loader2, Eye, EyeOff, Sparkles } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import { motion, AnimatePresence } from 'framer-motion';

import { useBoard } from '../../../context/BoardContext';
import { BoardCanvas } from './Board';
import { Toolbar } from './Toolbar';
import { SaveModal, ExitConfirmation, ArticleForm, ImageUploadModal } from './Modals';
import AIChatSidebar from './AIChatSidebar';
import ArticleImageUploadModal from './Modals/ArticleImageUploadModal';
import TextNode from './Items/TextNode';
import StickyNote from './Items/StickyNote';
import ArticleNode from './Items/ArticleNode';
import ImageNode from './Items/ImageNode';
import Connection from './Connections/Connection';
import StrokeRenderer from './Strokes/StrokeRenderer';
import RemoteCursors from './Board/RemoteCursors';
import { usePenTool } from '../../../hooks';
import { isImageNode, ArticleFormState, BoardViewState, Position, Board, BoardSharingRecord, BoardItem } from '../../../types';
import { itemRegistry } from '../../../services';
import { uploadBoardPreview } from '../../../utils/uploadBoardPreview';
import { generateBoardSVG } from '../../../utils/boardSvgGenerator';
import { ZoomSlider } from './ZoomControl';
import { MIN_SCALE, MAX_SCALE } from '../../../hooks/useZoomAndPan';
import { Button } from '@/components/ui/button';
import { useZoomAndPan } from '../../../hooks/useZoomAndPan';
import { createViewportFocuser, applyVisualFeedback, VIEWPORT_ANIMATION_PRESETS, ViewportElement } from '../../../utils/viewportUtils';
import { createOperationFeedback, OperationToast } from '../../../utils/operationFeedback';

// Read-only components
import StickyNoteReadOnly from '../public-board/StickyNoteReadOnly';
import TextNodeReadOnly from '../public-board/TextNodeReadOnly';
import ArticleNodeReadOnly from '../public-board/ArticleNodeReadOnly';
import ImageNodeReadOnly from '../public-board/ImageNodeReadOnly';
import ConnectionReadOnly from '../public-board/ConnectionReadOnly';
import { AIChatSidebarRef } from './AIChatSidebar';

interface ContextRectangle {
  id: string;
  rect: { x: number; y: number; width: number; height: number };
  itemIds: string[];
  color: string;
}

/**
 * Refactored detective board component
 */
const DetectiveBoard: React.FC = () => {
  const router = useRouter();
  const boardContainerRef = useRef<HTMLDivElement>(null);
  const [isBackLoading, setIsBackLoading] = useState<boolean>(false);
  const [isAIChatSidebarOpen, setIsAIChatSidebarOpen] = useState(false);
  const [aiSidebarWidth, setAISidebarWidth] = useState(400);
  const [isResizingAISidebar, setIsResizingAISidebar] = useState(false);
  const [isArticleImageModalOpen, setIsArticleImageModalOpen] = useState(false);
  const [editingArticleId, setEditingArticleId] = useState<string | null>(null);
  
  // State for selection rectangle
  const [selectionRect, setSelectionRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null);
  const [isSelecting, setIsSelecting] = useState<boolean>(false);
  const selectionStartPointRef = useRef<{ x: number; y: number } | null>(null);
  
  // State for persistent selection rectangle that follows selected items
  const [persistentSelectionRect, setPersistentSelectionRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null);
  
  const [contextRectangles, setContextRectangles] = useState<ContextRectangle[]>([]);
  const [isAddingToChat, setIsAddingToChat] = useState(false);

  // Define colors for different context rectangles (matching sidebar)
  const contextColors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];
  
  // Add loading state
  const [isBoardLoading, setIsBoardLoading] = useState(true);
  const [loadedItemIds, setLoadedItemIds] = useState<Set<string>>(new Set());
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);
  const [loadingBatch, setLoadingBatch] = useState<boolean>(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [initialBoardFetchComplete, setInitialBoardFetchComplete] = useState<boolean>(false);
  const [aiCreatedElementIds, setAiCreatedElementIds] = useState<Set<string>>(new Set());

  // State for operation feedback
  const [operationFeedback, setOperationFeedback] = useState<{
    isVisible: boolean;
    operationType: 'create' | 'update' | 'delete' | 'connect';
    message?: string;
  } | null>(null);
  const [deletingElementIds, setDeletingElementIds] = useState<Set<string>>(new Set());
  const [deletingConnectionIds, setDeletingConnectionIds] = useState<Set<string>>(new Set());

  const {
    board,
    boardId,
    scale,
    setScale,
    position,
    setPosition,
    setScaleAndPosition,
    selectedItemId,
    connections,
    connectMode,
    connectStart,
    toggleConnectMode,
    startConnection,
    completeConnection,
    cancelConnection,
    deleteConnection,
    addConnection,
    addBatchConnections,
    addItem,
    addBatchItems,
    updateItem,
    deleteItem,
    updateItemPosition,
    updateItemContent,
    selectItem,
    handleWheel,
    handleCanvasPointerDown: originalHandleCanvasPointerDown,
    modalState,
    openArticleForm,
    closeArticleForm,
    openSaveModal,
    closeSaveModal,
    openExitConfirmation,
    closeExitConfirmation,
    openImageUploadModal,
    closeImageUploadModal,
    setArticleFormData,
    handleArticleFormSubmit,
    handleSaveFormSubmit,
    saveBoard,
    lastSaved,
    isPenModeActive,
    togglePenMode,
    addStroke,
    removeStrokesNearPoint,
    isErasing,
    toggleEraserMode,
    presentationMode,
    togglePresentationMode,
    isGrabbing,
    boardDimensions,
    setBoard,
    remoteCursors,
    isSelectionModeActive,
    toggleSelectionMode,
    selectedItemIds,
    setSelectedItemIds,
    isCreatingNewBoard
  } = useBoard();

  const boardRef = useRef(board);
  const connectionsRef = useRef(connections);
  const aiChatRef = useRef<AIChatSidebarRef>(null);

  useEffect(() => {
    boardRef.current = board;
  }, [board]);

  useEffect(() => {
    connectionsRef.current = connections;
  }, [connections]);

  // Add AI viewport movement listener
  useEffect(() => {
    const handleAIViewportMove = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { targetPosition, zoomLevel, elementId } = customEvent.detail;
      
      try {
        if (elementId) {
          // Focus on a specific element
          const element = board.elements.find(el => el.id === elementId);
          if (element) {
            // Center the element in the viewport
            const containerRect = boardContainerRef.current?.getBoundingClientRect();
            if (containerRect) {
              const centerX = containerRect.width / 2;
              const centerY = containerRect.height / 2;
              
              // Calculate position to center the element
              const newPosition = {
                x: centerX - (element.position.x * (zoomLevel || scale)),
                y: centerY - (element.position.y * (zoomLevel || scale))
              };
              
              setPosition(newPosition);
              if (zoomLevel) {
                setScale(Math.max(MIN_SCALE, Math.min(MAX_SCALE, zoomLevel)));
              }
              
              // Briefly highlight the element
              selectItem(element.id);
              setTimeout(() => selectItem(null), 2000);
              
            }
          } else {
            toast.error(`Element with ID ${elementId} not found`);
          }
        } else if (targetPosition) {
          // Move to specific coordinates
          const containerRect = boardContainerRef.current?.getBoundingClientRect();
          if (containerRect) {
            const centerX = containerRect.width / 2;
            const centerY = containerRect.height / 2;
            
            const newPosition = {
              x: centerX - (targetPosition.x * (zoomLevel || scale)),
              y: centerY - (targetPosition.y * (zoomLevel || scale))
            };
            
            setPosition(newPosition);
            if (zoomLevel) {
              setScale(Math.max(MIN_SCALE, Math.min(MAX_SCALE, zoomLevel)));
            }
            
          }
        }
      } catch (error) {
        console.error('Error handling AI viewport movement:', error);
        toast.error('Failed to move viewport');
      }
    };

    // Listen for AI viewport movement events
    window.addEventListener('aiViewportMove', handleAIViewportMove);
    
    return () => {
      window.removeEventListener('aiViewportMove', handleAIViewportMove);
    };
  }, [board.elements, scale, setPosition, setScale, selectItem]);

  // Note: Removed old AI element event listeners as they were causing duplicate element creation
  // AI elements are now handled through the handleCreateAIElements callback from AIChatSidebar

  // Setup Pen Tool Hook
  const {
    currentStroke,
    isDrawing,
    handlePointerDown: handlePenPointerDown,
    handlePointerMove: handlePenPointerMove,
    handlePointerUp: handlePenPointerUp,
    penColor,
    penStrokeWidth,
    setPenColor,
    setPenStrokeWidth,
    hoveredStrokeId
  } = usePenTool({
    addStroke,
    removeStrokesNearPoint,
    boardRef: boardContainerRef,
    scale,
    offset: position,
    isErasing,
    strokes: board.strokes
  });

  // Modified pointer down handler to include background deselection and start selection
  const handleBoardPointerDown = useCallback((event: React.PointerEvent<Element>) => {
    if (isPenModeActive) {
      handlePenPointerDown(event);
    } else if (isSelectionModeActive) {
      const clickedItemElement = (event.target as HTMLElement).closest('[data-selectable-item="true"]');
      const clickedButton = (event.target as HTMLElement).closest('button');
      
      // Check if we clicked on the "Add to chat" button or any other button
      if (clickedButton) {
        return; // Let the button handle its own click event
      }

      if (clickedItemElement) {
        // A selectable item was clicked. Let BoardItem's onPointerDown handle it.
        // BoardItem's onPointerDown calls `onSelect(id)`.
        // The `selectItem` in BoardContext will then:
        //  - If the item is part of multi-select: (This case is TBD, BoardItem will handle group drag start)
        //  - If the item is NOT part of multi-select: Select it, clear multi-select, and turn off selection mode.
        //  - If the item IS part of multi-select and BoardItem initiates drag: group drag starts.
        
        // We should not start a selection rectangle here.
        setIsSelecting(false);
        selectionStartPointRef.current = null;
        setSelectionRect(null);
        
        // Call originalHandleCanvasPointerDown IF we want canvas panning when dragging an item.
        // However, BoardItem now manages its own drag logic more comprehensively.
        // If the click is on an item, let the item handle it completely.
        // The item's onPointerDown will eventually call `onSelect` or initiate its drag.
        // No need to call originalHandleCanvasPointerDown here if item handles its own interactions.
        return; // Let BoardItem handle the event fully.

      } else {
        // Clicked on the canvas background while in selection mode.
        // Start drawing selection rectangle.
        // Clear previous multi-selection before starting a new one.
        if (setSelectedItemIds) setSelectedItemIds([]);
        // Also clear single selection if any for good measure, though setSelectedItemIds should do it.
        selectItem(null);

        if (boardContainerRef.current) {
          const rect = boardContainerRef.current.getBoundingClientRect();
          const x = (event.clientX - rect.left - position.x) / scale;
          const y = (event.clientY - rect.top - position.y) / scale;
          selectionStartPointRef.current = { x, y };
          setIsSelecting(true);
          setSelectionRect({ x, y, width: 0, height: 0 });
        }
        // Prevent event from bubbling up to avoid triggering canvas pan
        event.preventDefault();
        event.stopPropagation();
      }
    } else {
      // Original logic for non-selection, non-pen mode (e.g. click on canvas to deselect)
      const clickedItemElement = (event.target as HTMLElement).closest('[data-selectable-item="true"]');
      if (!clickedItemElement) {
        // Clicked on canvas background (not on an item) when NOT in selection mode.
        if (document.activeElement instanceof HTMLElement) {
            document.activeElement.blur();
        }
        window.getSelection()?.removeAllRanges();
        selectItem(null); // Deselects single item, and BoardContext's selectItem clears multi-selection.
      }
      // If an item was clicked (not on canvas background) OR if general canvas pan is needed:
      // Let original handler manage single item selection (via BoardItem's onPointerDown -> onSelect)
      // and canvas panning if nothing was hit.
      originalHandleCanvasPointerDown(event);
    }
  }, [
    isPenModeActive, handlePenPointerDown, 
    isSelectionModeActive, originalHandleCanvasPointerDown, 
    selectItem, setSelectedItemIds, 
    scale, position, setIsSelecting, setSelectionRect,
    boardContainerRef // Added boardContainerRef
  ]);

  // Pointer move handler for selection
  const handleSelectionPointerMove = useCallback((event: React.PointerEvent<Element>) => {
    if (!isSelecting || !selectionStartPointRef.current || !boardContainerRef.current) return;

    const rect = boardContainerRef.current.getBoundingClientRect();
    const currentX = (event.clientX - rect.left - position.x) / scale;
    const currentY = (event.clientY - rect.top - position.y) / scale;

    const newRect = {
      x: Math.min(selectionStartPointRef.current.x, currentX),
      y: Math.min(selectionStartPointRef.current.y, currentY),
      width: Math.abs(selectionStartPointRef.current.x - currentX),
      height: Math.abs(selectionStartPointRef.current.y - currentY),
    };
    setSelectionRect(newRect);
  }, [isSelecting, scale, position, setSelectionRect]);

  // Pointer up handler for selection
  const handleSelectionPointerUp = useCallback((event: React.PointerEvent<Element>) => {
    if (!isSelecting || !selectionRect || !setSelectedItemIds) {
      setIsSelecting(false);
      selectionStartPointRef.current = null;
      setSelectionRect(null);
      return;
    }

    setIsSelecting(false);
    selectionStartPointRef.current = null;

    const itemsInSelection = board.elements.filter(item => {
      if (!item.position || typeof item.width !== 'number' || typeof item.height !== 'number') return false;
      // Simple Bounding Box Collision Detection
      const itemRight = item.position.x + item.width;
      const itemBottom = item.position.y + item.height;
      const rectRight = selectionRect.x + selectionRect.width;
      const rectBottom = selectionRect.y + selectionRect.height;

      return (
        item.position.x < rectRight &&
        itemRight > selectionRect.x &&
        item.position.y < rectBottom &&
        itemBottom > selectionRect.y
      );
    }).map(item => item.id);

    setSelectedItemIds(itemsInSelection);
    
    // Clear the selection rectangle after a brief delay to show the selection visually
    setTimeout(() => {
      setSelectionRect(null);
    }, 100);

  }, [isSelecting, selectionRect, board.elements, setSelectedItemIds, scale, position]);

  // Function to calculate bounding box of selected items
  const calculateSelectionBounds = useCallback(() => {
    if (!selectedItemIds || selectedItemIds.length === 0) {
      setPersistentSelectionRect(null);
      return;
    }

    const selectedItems = board.elements.filter(item => selectedItemIds.includes(item.id));
    if (selectedItems.length === 0) {
      setPersistentSelectionRect(null);
      return;
    }

    // Find the bounds of all selected items
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    selectedItems.forEach(item => {
      if (!item.position || typeof item.width !== 'number' || typeof item.height !== 'number') {
        return;
      }
      
      const itemLeft = item.position.x;
      const itemTop = item.position.y;
      const itemRight = item.position.x + item.width;
      const itemBottom = item.position.y + item.height;

      minX = Math.min(minX, itemLeft);
      minY = Math.min(minY, itemTop);
      maxX = Math.max(maxX, itemRight);
      maxY = Math.max(maxY, itemBottom);
    });

    // Add some padding around the selection
    const padding = 10;
    setPersistentSelectionRect({
      x: minX - padding,
      y: minY - padding,
      width: maxX - minX + (padding * 2),
      height: maxY - minY + (padding * 2)
    });
  }, [selectedItemIds, board.elements]);

  // Update persistent selection rectangle when selected items or their positions change
  useEffect(() => {
    calculateSelectionBounds();
  }, [calculateSelectionBounds]);

  // Handle back button click
  const handleBackClick = () => {
    setIsBackLoading(true);
    // Determine where we should return to
    const referrer = document.referrer;
    let destination = '/';
    
    if (referrer.includes('/recent')) {
      destination = '/recent';
    }
    
    // Always navigate directly without confirmation
    router.push(destination);
  };
  
  // Handle saving before exit
  const handleSaveBeforeExit = async () => {
    await saveBoard();
    closeExitConfirmation();
    router.push(modalState.exitDestination);
  };

  // Handle exit without saving
  const handleExitWithoutSaving = () => {
    closeExitConfirmation();
    router.push(modalState.exitDestination);
  };

  // Handle connection deletion
  const handleDeleteConnection = (id: string) => {
    deleteConnection(id);
  };

  // Add a handler for the image upload submission
  const handleImageUploadSubmit = ({ file_url, alt, localImageUrl }: { file_url: string; alt: string; localImageUrl?: string }) => {
    // Add the image element to the board state, providing the object path as file_url
    // Use the localImageUrl immediately if available
    addItem('image', undefined, { 
      file_url: file_url,
      alt: alt,
      imageUrl: localImageUrl
    });
  };

  // Handle article form submissions
  const handleArticleSubmit = (data: ArticleFormState) => {
    // Process the article form result and add the item to the board
    console.log('Adding article with data:', data);
    
    // Add the article item to the board
    addItem('article', undefined, {
      title: data.title,
      url: data.url,
      content: data.content,
      imageUrl: data.imageUrl,
      website_url: data.website_url || data.url,
      file_url: data.file_url
    });
    
    // Return the data for consistency with the interface
    return data;
  };

  // Function to render connections with improved simplification logic
  const renderConnections = () => {
    
    if (!connections || connections.length === 0) {
      return null;
    }

    // Determine simplification level based on total number of connections
    // rather than just visible connections
    let simplificationLevel = 0;
    const totalConnections = connections.length;
    
    if (totalConnections > 50) {
      simplificationLevel = 3; // Highest simplification for very large boards
    } else if (totalConnections > 30) {
      simplificationLevel = 2; // High simplification
    } else if (totalConnections > 15) {
      simplificationLevel = 1; // Moderate simplification
    }

    // Filter to only show connections where both items are loaded
    const renderedConnections = connections.filter(connection => {
      if (!loadedItemIds.has(connection.fromId) || !loadedItemIds.has(connection.toId)) {
        return false;
      }
      
      const fromItem = board.elements.find(item => item.id === connection.fromId);
      const toItem = board.elements.find(item => item.id === connection.toId);
      return fromItem && toItem;
    });

    return renderedConnections.map((connection, index) => {
      const from = board.elements.find(item => item.id === connection.fromId);
      const to = board.elements.find(item => item.id === connection.toId);
      
      if (!from || !to) {
        return null;
      }

      // Wrap connection with motion.div for animation
      const animatedConnection = (component: React.ReactElement) => {
        const isDeleting = deletingConnectionIds.has(connection.id);

        return (
          <motion.div
            key={connection.id}
            initial={{ opacity: 0 }}
            animate={{
              opacity: isDeleting ? 0.3 : 1,
              filter: isDeleting ? 'grayscale(70%)' : 'none'
            }}
            transition={{
              duration: isDeleting ? 0.3 : 0.4,
              delay: isDeleting ? 0 : 0.1 + (index * 0.05) // Stagger connections appearing
            }}
            style={{ pointerEvents: isDeleting ? 'none' : 'auto' }}
          >
            {component}
          </motion.div>
        );
      };

      if (presentationMode) {
        return animatedConnection(
          <ConnectionReadOnly
            key={connection.id}
            id={connection.id}
            from={{ id: from.id, position: from.position, width: from.width, height: from.height }}
            to={{ id: to.id, position: to.position, width: to.width, height: to.height }}
            simplificationLevel={simplificationLevel}
          />
        );
      }

      return animatedConnection(
        <Connection
          key={connection.id}
          id={connection.id}
          from={{ id: from.id, position: from.position, width: from.width, height: from.height }}
          to={{ id: to.id, position: to.position, width: to.width, height: to.height }}
          onSelect={selectItem}
          isSelected={selectedItemId === connection.id}
          onDelete={handleDeleteConnection}
          presentationMode={presentationMode}
          simplificationLevel={simplificationLevel}
        />
      );
    });
  };

  // Add a function to determine which items are in the current viewport
  const getItemsInViewport = useCallback(() => {
    if (!boardContainerRef.current) return [];
    
    const containerRect = boardContainerRef.current.getBoundingClientRect();
    const viewportX = -position.x / scale;
    const viewportY = -position.y / scale;
    const viewportWidth = containerRect.width / scale;
    const viewportHeight = containerRect.height / scale;
    
    // Create a slightly larger viewport for pre-loading nearby items
    const expandedViewport = {
      minX: viewportX - viewportWidth * 0.5,
      minY: viewportY - viewportHeight * 0.5,
      maxX: viewportX + viewportWidth * 1.5,
      maxY: viewportY + viewportHeight * 1.5
    };
    
    return board.elements.filter(item => {
      if (!item.position) return false;
      
      const itemRight = item.position.x + (item.width || 0);
      const itemBottom = item.position.y + (item.height || 0);
      
      return (
        item.position.x < expandedViewport.maxX &&
        itemRight > expandedViewport.minX &&
        item.position.y < expandedViewport.maxY &&
        itemBottom > expandedViewport.minY
      );
    });
  }, [board.elements, position, scale]);

  // Add lazy loading effect with visual loading states
  useEffect(() => {
    if (board.elements.length === 0) {
      // Empty board - set as fully loaded immediately
      setLoadingProgress(100);
      setTimeout(() => {
        setIsBoardLoading(false);
        setInitialBoardFetchComplete(true);
      }, 500);
      return;
    }
    
    // Skip loading process if initial board load is already complete
    if (initialBoardFetchComplete) {
      // Just add any new elements to loadedItemIds without showing loading UI
      const newElements = board.elements.filter(item => !loadedItemIds.has(item.id));
      if (newElements.length > 0) {
        const newLoadedIds = new Set(loadedItemIds);
        newElements.forEach(item => newLoadedIds.add(item.id));
        setLoadedItemIds(newLoadedIds);
      }
      return;
    }
    
    // On initial load, only load items in the viewport
    if (isInitialLoad && board.elements.length > 0) {
      setLoadingProgress(20);
      const itemsInView = getItemsInViewport();
      const initialItems = itemsInView.length > 0 
        ? itemsInView 
        : board.elements.slice(0, Math.min(15, board.elements.length));
      
      setLoadedItemIds(new Set(initialItems.map(item => item.id)));
      setIsInitialLoad(false);
      setLoadingProgress(50);
      
      // Start a timer to simulate progress towards 90%
      const timer = setTimeout(() => {
        setLoadingProgress(90);
      }, 300);
      
      return () => clearTimeout(timer);
    }
    
    // After initial load, keep loading more items in batches
    if (!loadingBatch && loadedItemIds.size < board.elements.length) {
      setLoadingBatch(true);
      
      setTimeout(() => {
        // Prioritize items in viewport that aren't loaded yet
        const itemsInView = getItemsInViewport()
          .filter(item => !loadedItemIds.has(item.id));
        
        // If no items in view need loading, load some off-screen items
        const itemsToLoad = itemsInView.length > 0 
          ? itemsInView 
          : board.elements
              .filter(item => !loadedItemIds.has(item.id))
              .slice(0, 10); // Load 10 at a time
        
        if (itemsToLoad.length > 0) {
          const newLoadedIds = new Set(loadedItemIds);
          itemsToLoad.forEach(item => newLoadedIds.add(item.id));
          setLoadedItemIds(newLoadedIds);
          
          // Calculate new progress
          const newProgress = Math.min(95, 50 + (newLoadedIds.size / board.elements.length * 50));
          setLoadingProgress(newProgress);
      } else {
          // All items loaded
          setLoadingProgress(100);
          setTimeout(() => {
            setIsBoardLoading(false);
            setInitialBoardFetchComplete(true);
          }, 500);
        }
        
        setLoadingBatch(false);
      }, 100); // Short delay to not block rendering
    } else if (loadedItemIds.size >= board.elements.length) {
      // All items loaded
      setLoadingProgress(100);
      setTimeout(() => {
        setIsBoardLoading(false);
        setInitialBoardFetchComplete(true);
      }, 500);
    }
  }, [board.elements, loadedItemIds, isInitialLoad, loadingBatch, getItemsInViewport, initialBoardFetchComplete]);

  // Update loaded items when viewport changes
  useEffect(() => {
    if (isInitialLoad) return;
    
    // When user navigates, prioritize loading items in the new viewport
    const itemsInView = getItemsInViewport();
    const unloadedViewportItems = itemsInView.filter(item => !loadedItemIds.has(item.id));
    
    if (unloadedViewportItems.length > 0) {
      const newLoadedIds = new Set(loadedItemIds);
      unloadedViewportItems.forEach(item => newLoadedIds.add(item.id));
      setLoadedItemIds(newLoadedIds);
    }
  }, [position, scale, isInitialLoad, getItemsInViewport, loadedItemIds]);

  // Replace the simple LoadingIndicator with a more appealing version
  const LoadingIndicator = () => {
    const totalItems = board.elements.length;
    const loadedItems = loadedItemIds.size;
    
    // If the initial board fetch is complete, never show the indicator again.
    if (initialBoardFetchComplete) return null;
    
    // Original condition (can be kept or removed, as the above check handles the 'after initial load' case)
    // if (loadedItems >= totalItems && !isBoardLoading) return null; 
    
        return (
      <div className="absolute bottom-4 right-4 glass-morphism px-3 py-2 rounded-md text-white text-sm z-50 flex items-center space-x-2">
        <div className="w-4 h-4 relative">
          <Loader2 className="w-4 h-4 animate-spin absolute" />
        </div>
        <div className="flex flex-col">
          <span>Loading {loadedItems}/{totalItems} items</span>
          <div className="w-full bg-gray-600 rounded-full h-1.5 mt-1 overflow-hidden">
            <div 
              className="bg-blue-500 h-1.5 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${(loadedItems / Math.max(totalItems, 1)) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>
    );
  };

  // Full screen loading overlay for initial load
  const LoadingOverlay = () => {
    // Prioritize showing this overlay if a new board is being created and redirect is pending.
    if (isCreatingNewBoard) {
      return (
        <div className="fixed inset-0 flex flex-col items-center justify-center bg-noir-900 z-[100]">
          <Loader2 className="h-16 w-16 animate-spin text-noir-accent mb-6" />
          <p className="text-white text-xl font-medium mb-2">Creating your board...</p>
          <p className="text-gray-400 text-sm">Please wait, redirecting soon...</p>
        </div>
      );
    }

    // Original condition for general board loading
    if ((!isBoardLoading && loadingProgress >= 100) || modalState.showSaveModal || initialBoardFetchComplete) return null;

      return (
      <div className="fixed inset-0 flex flex-col items-center justify-center bg-noir-900 z-[100]">
        <Loader2 className="h-16 w-16 animate-spin text-noir-accent mb-6" />
        <p className="text-white text-xl font-medium mb-2">Loading detective board...</p>
        <p className="text-gray-400 text-sm">
          {loadingProgress < 30 
            ? "Preparing your investigation..." 
            : loadingProgress < 60 
            ? "Loading elements..." 
            : loadingProgress < 90 
            ? "Arranging connections..." 
            : "Almost ready..."}
        </p>
        
        {/* Simple progress bar */}
        <div className="mt-8 w-64 bg-gray-700 rounded-full h-2 overflow-hidden">
          <div 
            className="bg-noir-accent h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${loadingProgress}%` }}
          ></div>
        </div>
      </div>
    );
  };

  // Calculate start position for connection line preview
  const connectStartItemPosition = useMemo(() => {
    if (!connectStart) return null;
    return board.elements.find(item => item.id === connectStart)?.position || null;
  }, [connectStart, board.elements]);

  // Prepare view state object for BoardCanvas
  const boardViewState: BoardViewState = useMemo(() => ({
    scale,
    position,
    selectedItemId,
    connectMode,
    connectStart,
    isGrabbing,
    presentationMode
  }), [scale, position, selectedItemId, connectMode, connectStart, isGrabbing, presentationMode]);

  // Calculate isNewBoard based on boardId
  const isNewBoard = !boardId || boardId === 'new';

  // Automatically open save modal when it's a new board
  useEffect(() => {
    if (isNewBoard) {
      // Small delay to ensure the board is rendered first
      const timer = setTimeout(() => {
        openSaveModal();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isNewBoard, openSaveModal]);

  const handleVisibilityToggle = useCallback((id: string, isVisible: boolean) => {
    const item = board.elements.find(item => item.id === id);
    if (item && isImageNode(item)) {
      const newType = isVisible ? 'image' : 'image-invisible';
      // Only update if the type actually needs to change
      if (item.type !== newType) {
        updateItem(id, { type: newType });
      }
    }
  }, [board.elements, updateItem]);

  // Toggle AI Chat sidebar
  const handleToggleAIChat = () => {
    setIsAIChatSidebarOpen(prev => !prev);
  };

  // Handle adding insights from AI
  const handleAddAIInsight = useCallback(async (content: string) => {
    // Add the AI insight as a sticky note
    addItem('sticky-yellow', undefined, { content });
  }, [addItem]);

  // Handle closing AI chat sidebar
  const handleCloseAIChat = useCallback(() => {
    setIsAIChatSidebarOpen(false);
  }, []);

  // Handle AI sidebar resize
  const handleAISidebarResizeStart = useCallback(() => {
    setIsResizingAISidebar(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      const newWidth = window.innerWidth - e.clientX;
      const minWidth = 320;
      const maxWidth = 600;
      setAISidebarWidth(Math.max(minWidth, Math.min(maxWidth, newWidth)));
    };

    const handleMouseUp = () => {
      setIsResizingAISidebar(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, []);
  
  // Simplified viewport adjustment function
  const adjustViewportToCreatedElements = useCallback((elementsData: Array<{position: {x: number, y: number}, type: string}>, delay: number = 50) => {
    console.log(`[Viewport Adjustment] Scheduling viewport adjustment for ${elementsData.length} elements:`, elementsData);

    // Add a minimal delay to ensure elements are rendered
    setTimeout(() => {
      try {
        if (elementsData.length === 0 || !boardContainerRef.current) {
          console.warn(`[Viewport Adjustment] No elements or container ref`);
          return;
        }

        // For single element, just center it
        if (elementsData.length === 1) {
          const element = elementsData[0];
          const containerRect = boardContainerRef.current.getBoundingClientRect();

          console.log(`[Viewport Adjustment] Element position:`, element.position);

          // Validate element position
          if (!element.position || typeof element.position.x !== 'number' || typeof element.position.y !== 'number') {
            console.error(`[Viewport Adjustment] Invalid element position:`, element.position);
            return;
          }

          console.log(`[Viewport Adjustment] Centering single element at (${element.position.x}, ${element.position.y})`);
          console.log(`[Viewport Adjustment] Container dimensions: ${containerRect.width}x${containerRect.height}`);

          // Calculate position to center the element
          const centerX = containerRect.width / 2;
          const centerY = containerRect.height / 2;

          // Get current scale from the board context to avoid stale closure values
          const getCurrentScale = () => scale;
          const currentScale = getCurrentScale();

          // Use a more appropriate zoom level - zoom in enough to make the element clearly visible
          // If currently zoomed out, zoom in to at least 1.0, if already zoomed in, use a reasonable level
          const targetScale = currentScale < 0.8 ? 1.2 : Math.max(1.0, Math.min(2.0, currentScale * 1.2));

          console.log(`[Viewport Adjustment] Current scale:`, currentScale, `-> Target scale:`, targetScale);
          console.log(`[Viewport Adjustment] Element position:`, element.position);
          console.log(`[Viewport Adjustment] Scale difference:`, Math.abs(targetScale - currentScale));

          // Check if the scale change is significant enough
          if (Math.abs(targetScale - currentScale) < 0.01) {
            console.log(`[Viewport Adjustment] Scale change too small, skipping scale adjustment`);
            // Just adjust position
            const newPosition = {
              x: centerX - (element.position.x * currentScale),
              y: centerY - (element.position.y * currentScale)
            };
            console.log(`[Viewport Adjustment] Only adjusting position to:`, newPosition);
            setPosition(newPosition);
            return;
          }

          // Apply both scale and position adjustments using the new setScaleAndPosition function
          console.log(`[Viewport Adjustment] Applying scale adjustment from ${currentScale} to ${targetScale}`);

          // Debug the scale comparison issue
          console.log(`[Viewport Adjustment] Current scale before adjustment:`, currentScale);
          console.log(`[Viewport Adjustment] Target scale:`, targetScale);
          console.log(`[Viewport Adjustment] Scale comparison (currentScale === targetScale):`, currentScale === targetScale);
          console.log(`[Viewport Adjustment] Scale difference:`, Math.abs(currentScale - targetScale));
          console.log(`[Viewport Adjustment] MIN_SCALE:`, MIN_SCALE, `MAX_SCALE:`, MAX_SCALE);

          // Check if target scale is within bounds
          const constrainedTargetScale = Math.min(Math.max(targetScale, MIN_SCALE), MAX_SCALE);
          console.log(`[Viewport Adjustment] Constrained target scale:`, constrainedTargetScale);
          console.log(`[Viewport Adjustment] Scale will change:`, constrainedTargetScale !== currentScale);

          // Calculate the position that will center the element at the new scale
          const targetPosition = {
            x: centerX - (element.position.x * constrainedTargetScale),
            y: centerY - (element.position.y * constrainedTargetScale)
          };

          console.log(`[Viewport Adjustment] Target position to center element:`, targetPosition);

          // Use the new setScaleAndPosition function to apply both changes atomically
          // This prevents the issue where setPosition overrides the scale
          console.log(`[Viewport Adjustment] Calling setScaleAndPosition with scale:`, constrainedTargetScale, `position:`, targetPosition);
          setScaleAndPosition(constrainedTargetScale, targetPosition);

          // Provide immediate success feedback
          console.log(`[Viewport Adjustment] ✅ Viewport adjustment completed!`);
          console.log(`[Viewport Adjustment] Element should now be centered and properly scaled in the viewport`);

          // Optional: Show user feedback
          // toast.success(`Element centered and zoomed in view`, { duration: 1500 });

          return;
        }

        // For multiple elements, calculate bounding box
        const containerRect = boardContainerRef.current.getBoundingClientRect();

        // Initialize bounding box variables
        let minX = elementsData[0].position.x;
        let maxX = elementsData[0].position.x;
        let minY = elementsData[0].position.y;
        let maxY = elementsData[0].position.y;

        elementsData.forEach(elementData => {
          // Get default dimensions based on element type
          let width = 200; // Default width
          let height = 200; // Default height

          // Set type-specific dimensions
          switch (elementData.type) {
            case 'sticky-yellow':
            case 'sticky-red':
            case 'sticky-blue':
              width = 200;
              height = 200;
              break;
            case 'text':
              width = 300;
              height = 150;
              break;
            case 'article':
              width = 300;
              height = 400;
              break;
            case 'image':
              width = 250;
              height = 250;
              break;
          }

          minX = Math.min(minX, elementData.position.x);
          maxX = Math.max(maxX, elementData.position.x + width);
          minY = Math.min(minY, elementData.position.y);
          maxY = Math.max(maxY, elementData.position.y + height);
        });

        // Calculate center point
        const centerX = (minX + maxX) / 2;
        const centerY = (minY + maxY) / 2;
        const elementWidth = maxX - minX;
        const elementHeight = maxY - minY;

        console.log(`[Viewport Adjustment] Multiple elements center: (${Math.round(centerX)}, ${Math.round(centerY)})`);
        console.log(`[Viewport Adjustment] Bounding box: ${Math.round(elementWidth)}x${Math.round(elementHeight)}`);

        // Calculate position to center the elements
        const centerPosX = containerRect.width / 2;
        const centerPosY = containerRect.height / 2;

        // Use a reasonable scale for multiple elements
        const targetScale = Math.max(0.3, Math.min(1.5, scale));

        const newPosition = {
          x: centerPosX - (centerX * targetScale),
          y: centerPosY - (centerY * targetScale)
        };

        console.log(`[Viewport Adjustment] Setting position for multiple elements:`, newPosition);

        // Apply the changes
        setPosition(newPosition);
        if (targetScale !== scale) {
          setScale(targetScale);
        }
      } catch (error) {
        console.error('[Viewport Adjustment] Error auto-adjusting viewport:', error);
      }
    }, delay);
  }, [boardContainerRef, setPosition, setScale]); // Removed scale and position from dependencies to avoid stale closures

  // Create viewport focuser for consistent viewport management
  const focusOnElements = useMemo(() =>
    createViewportFocuser(
      boardContainerRef,
      setScaleAndPosition,
      () => scale
    ),
    [setScaleAndPosition, scale]
  );

  // Focus viewport on connections - shows both connected elements
  const focusOnConnection = useCallback(async (connectionIds: string[]) => {
    try {
      console.log(`[Connection Focus] ===== STARTING CONNECTION FOCUS =====`);
      console.log(`[Connection Focus] Focusing on ${connectionIds.length} connections:`, connectionIds);
      console.log(`[Connection Focus] Available connections:`, connections.map(c => ({ id: c.id, fromId: c.fromId, toId: c.toId })));
      console.log(`[Connection Focus] Available elements:`, board.elements.map(e => ({ id: e.id, position: e.position })));

      // Get all unique element IDs from the connections
      const elementIds = new Set<string>();
      connectionIds.forEach(connId => {
        const connection = connections.find(c => c.id === connId);
        console.log(`[Connection Focus] Looking for connection ${connId}:`, connection);
        if (connection) {
          elementIds.add(connection.fromId);
          elementIds.add(connection.toId);
          console.log(`[Connection Focus] Added element IDs: ${connection.fromId}, ${connection.toId}`);
        } else {
          console.warn(`[Connection Focus] Connection ${connId} not found in connections array`);
        }
      });

      console.log(`[Connection Focus] Unique element IDs to focus:`, Array.from(elementIds));

      // Get the actual elements from the board
      const elementsToFocus: ViewportElement[] = [];
      elementIds.forEach(elementId => {
        const element = board.elements.find(e => e.id === elementId);
        console.log(`[Connection Focus] Looking for element ${elementId}:`, element);
        if (element) {
          elementsToFocus.push({
            position: element.position,
            width: element.width || 200,
            height: element.height || 100,
            type: element.type
          });
          console.log(`[Connection Focus] Added element to focus:`, element.id, element.position);
        } else {
          console.warn(`[Connection Focus] Element ${elementId} not found in board elements`);
        }
      });

      console.log(`[Connection Focus] Elements to focus:`, elementsToFocus);

      if (elementsToFocus.length > 0) {
        console.log(`[Connection Focus] Calling focusOnElements with ${elementsToFocus.length} elements`);
        await focusOnElements(elementsToFocus, VIEWPORT_ANIMATION_PRESETS.connection);
        console.log(`[Connection Focus] focusOnElements completed`);

        // Apply visual feedback to the connected elements
        console.log(`[Connection Focus] Applying visual feedback to elements:`, Array.from(elementIds));
        applyVisualFeedback(
          Array.from(elementIds),
          selectItem,
          setSelectedItemIds,
          { duration: 2000, type: 'highlight' }
        );
      } else {
        console.warn(`[Connection Focus] No elements found to focus on`);
      }

      console.log(`[Connection Focus] ===== CONNECTION FOCUS COMPLETED =====`);
    } catch (error) {
      console.error('[Connection Focus] Error focusing on connection:', error);
    }
  }, [connections, board.elements, focusOnElements, selectItem, setSelectedItemIds]);

  // Test function for debugging viewport focusing (can be called from browser console)
  useEffect(() => {
    (window as any).testConnectionFocus = () => {
      console.log('=== TESTING CONNECTION FOCUS ===');
      if (connections.length > 0) {
        const testConnectionId = connections[0].id;
        console.log('Testing with connection:', testConnectionId);
        focusOnConnection([testConnectionId]);
      } else {
        console.log('No connections available for testing');
      }
    };

    (window as any).testElementFocus = () => {
      console.log('=== TESTING ELEMENT FOCUS ===');
      if (board.elements.length > 0) {
        const testElement = board.elements[0];
        console.log('Testing with element:', testElement.id);
        const elementsToFocus: ViewportElement[] = [{
          position: testElement.position,
          width: testElement.width || 200,
          height: testElement.height || 100,
          type: testElement.type
        }];
        focusOnElements(elementsToFocus, VIEWPORT_ANIMATION_PRESETS.connection);
      } else {
        console.log('No elements available for testing');
      }
    };
  }, [connections, board.elements, focusOnConnection, focusOnElements]);

  // Keep a ref to the current board state to avoid stale closures
  const currentBoardRef = useRef(board);
  useEffect(() => {
    currentBoardRef.current = board;
  }, [board]);

  // Handle creating elements and connections from AI response
  const handleCreateAIElements = useCallback(async (elements: Array<{
    id: string;
    type: string;
    content?: string;
    title?: string;
    url?: string;
    position_x: number;
    position_y: number;
  }>,
  connections: Array<{
    from_id: string;
    to_id: string;
    label?: string;
  }>
) => {
    const idMap: Record<string, string> = {};
    const createdIds: string[] = [];
    try {
      console.log(`[handleCreateAIElements] Starting with ${elements.length} elements and ${connections.length} connections`);
      console.log(`[handleCreateAIElements] Current board has ${board.elements.length} elements`);
      
      // If we're only creating connections (no new elements), we need to ensure
      // the elements exist and are loaded
      if (elements.length === 0 && connections.length > 0) {
        console.log(`[handleCreateAIElements] Connection-only operation detected`);
        
        // Get all unique element IDs from the connections
        const requiredElementIds = new Set<string>();
        connections.forEach(conn => {
          requiredElementIds.add(conn.from_id);
          requiredElementIds.add(conn.to_id);
        });
        
        console.log(`[handleCreateAIElements] Required element IDs for connections:`, Array.from(requiredElementIds));
        
        // For connection-only operations, we need to wait a bit for the board state to be updated
        // from previous element creation operations
        console.log(`[handleCreateAIElements] Waiting for board state to stabilize...`);
        await new Promise(resolve => setTimeout(resolve, 200));
        
        // Now check the updated board state using the ref to get the current state
        console.log(`[handleCreateAIElements] Board elements after wait:`, currentBoardRef.current.elements.map(e => ({ id: e.id, type: e.type })));
        
        // Add all required elements to tracking sets
        setLoadedItemIds(prev => {
          const newSet = new Set(prev);
          requiredElementIds.forEach(id => {
            newSet.add(id);
            console.log(`[handleCreateAIElements] Added ${id} to loadedItemIds`);
          });
          return newSet;
        });
        
        // Also add to aiCreatedElementIds for tracking
        setAiCreatedElementIds(prev => {
          const newSet = new Set(prev);
          requiredElementIds.forEach(id => {
            newSet.add(id);
            console.log(`[handleCreateAIElements] Added ${id} to aiCreatedElementIds`);
          });
          return newSet;
        });
        
        // Wait for state updates to propagate
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      let createdItems: BoardItem[] = [];
      
      if (elements.length > 0) {
        // Convert AI elements to the format expected by addBatchItems, preserving their IDs
        const batchItems = elements.map(element => {
          console.log(`[handleCreateAIElements] Processing AI element: ${element.id} (${element.type})`);

          return {
            type: element.type,
            position: { x: element.position_x, y: element.position_y },
            props: {
              id: element.id, // CRITICAL: Preserve the AI-generated ID inside props
              content: element.content || '',
              title: element.title || (element.type === 'article' ? 'AI Article' : undefined),
              url: element.url || '',
              // For image elements, set file_url
              file_url: element.type === 'image' ? element.url : null,
              alt: element.type === 'image' ? (element.content || 'AI image') : undefined,
              // For article elements, set website_url
              website_url: element.type === 'article' ? element.url : undefined
            }
          };
        });

        // Create all elements in a single batch operation
        createdItems = await addBatchItems(batchItems);
      }

      // Immediately add the created items to both loadedItemIds and aiCreatedElementIds
      if (createdItems.length > 0) {
        setLoadedItemIds(prevLoadedIds => {
          const newLoadedIds = new Set(prevLoadedIds);
          createdItems.forEach(item => {
            newLoadedIds.add(item.id);
            console.log(`[handleCreateAIElements] Added ${item.id} to loadedItemIds`);
          });
          console.log(`[handleCreateAIElements] Updated loadedItemIds size: ${newLoadedIds.size}`);
          return newLoadedIds;
        });

        setAiCreatedElementIds(prevAiIds => {
          const newAiIds = new Set(prevAiIds);
          createdItems.forEach(item => {
            newAiIds.add(item.id);
            console.log(`[handleCreateAIElements] Added ${item.id} to aiCreatedElementIds`);
          });
          console.log(`[handleCreateAIElements] Updated aiCreatedElementIds size: ${newAiIds.size}`);
          return newAiIds;
        });
      }

      // Wait a short time to ensure the board state has been updated with server IDs
      await new Promise(resolve => setTimeout(resolve, 100));

      // Create ID mapping from AI IDs to final board IDs
      elements.forEach((aiElement, index) => {
        const createdItem = createdItems[index];
        if (createdItem) {
          // Since we're preserving IDs, this should usually be the same
          // But we still map in case the server changes the ID
          idMap[aiElement.id] = createdItem.id;
          createdIds.push(createdItem.id);
          console.log(`[handleCreateAIElements] ID mapping: AI ID ${aiElement.id} -> Board ID ${createdItem.id}`);
        }
      });

      // Update both loadedItemIds and aiCreatedElementIds with the final mapped IDs (in case server returned different IDs)
      if (createdIds.length > 0) {
        setLoadedItemIds(prevLoadedIds => {
          const newLoadedIds = new Set(prevLoadedIds);
          createdIds.forEach(id => {
            newLoadedIds.add(id);
          });
          return newLoadedIds;
        });

        setAiCreatedElementIds(prevAiIds => {
          const newAiIds = new Set(prevAiIds);
          createdIds.forEach(id => {
            newAiIds.add(id);
          });
          return newAiIds;
        });
      }

      // Create connections using the correct IDs (either mapped or direct from AI)
      if (connections.length > 0) {
        console.log(`[handleCreateAIElements] Preparing to create ${connections.length} connections in batch...`);
        console.log(`[handleCreateAIElements] Connection data received:`, connections);
        console.log(`[handleCreateAIElements] Available ID mappings:`, idMap);
        console.log(`[handleCreateAIElements] Created element IDs:`, createdItems.map(item => item.id));
        console.log(`[handleCreateAIElements] Current board elements:`, board.elements.map(e => ({ id: e.id, type: e.type })));

        // Prepare connections for batch operation
        const batchConnections = connections.map(conn => {
          // First try to use the IDs directly (for AI-generated connections with database IDs)
          let fromId = conn.from_id;
          let toId = conn.to_id;

          // When no elements were created in this call, skip the createdItems check
          // and go straight to checking the board state
          const noNewElements = createdItems.length === 0;
          
          if (!noNewElements) {
            // Check if these IDs exist in our created elements
            const fromExists = createdItems.some(item => item.id === fromId);
            const toExists = createdItems.some(item => item.id === toId);

            // If direct IDs don't exist, try mapping (for legacy compatibility)
            if (!fromExists && idMap[conn.from_id]) {
              fromId = idMap[conn.from_id];
              console.log(`[handleCreateAIElements] Mapped fromId: ${conn.from_id} -> ${fromId}`);
            }
            if (!toExists && idMap[conn.to_id]) {
              toId = idMap[conn.to_id];
              console.log(`[handleCreateAIElements] Mapped toId: ${conn.to_id} -> ${toId}`);
            }
          }

          // Final validation - check if both elements exist in:
          // 1. Items just created in this batch (if any)
          // 2. Current board elements
          // 3. Loaded item IDs (for lazy-loaded boards) - this is the most reliable source
          // 4. AI created element IDs (for elements created by AI in previous calls)
          
          // For connection-only operations, we need to be more lenient and check board.elements first
          // Use the ref to get the current board state, not the stale closure
          const checkInBoard = (id: string) => currentBoardRef.current.elements.some(item => item.id === id);
          const checkInLoaded = (id: string) => loadedItemIds.has(id);
          const checkInAICreated = (id: string) => aiCreatedElementIds.has(id);
          const checkInCreated = (id: string) => createdItems.length > 0 && createdItems.some(item => item.id === id);
          
          const finalFromExists = checkInCreated(fromId) || checkInBoard(fromId) || checkInLoaded(fromId) || checkInAICreated(fromId);
          const finalToExists = checkInCreated(toId) || checkInBoard(toId) || checkInLoaded(toId) || checkInAICreated(toId);
          
          // Additional debug logging for connection validation
          if (!finalFromExists || !finalToExists) {
            console.log(`[handleCreateAIElements] Detailed validation for connection ${fromId} -> ${toId}:`);
            console.log(`  From (${fromId}):`);
            console.log(`    - In createdItems: ${checkInCreated(fromId)}`);
            console.log(`    - In board.elements: ${checkInBoard(fromId)}`);
            console.log(`    - In loadedItemIds: ${checkInLoaded(fromId)}`);
            console.log(`    - In aiCreatedElementIds: ${checkInAICreated(fromId)}`);
            console.log(`  To (${toId}):`);
            console.log(`    - In createdItems: ${checkInCreated(toId)}`);
            console.log(`    - In board.elements: ${checkInBoard(toId)}`);
            console.log(`    - In loadedItemIds: ${checkInLoaded(toId)}`);
            console.log(`    - In aiCreatedElementIds: ${checkInAICreated(toId)}`);
          }

          if (!finalFromExists || !finalToExists) {
            console.warn(`[handleCreateAIElements] Invalid connection IDs: from '${fromId}' (exists: ${finalFromExists}) to '${toId}' (exists: ${finalToExists})`);
            console.warn(`[handleCreateAIElements] Available element IDs in createdItems:`, createdItems.map(item => item.id));
            console.warn(`[handleCreateAIElements] Available element IDs in board.elements:`, board.elements.map(item => item.id));
            console.warn(`[handleCreateAIElements] Available element IDs in loadedItemIds:`, Array.from(loadedItemIds));
            console.warn(`[handleCreateAIElements] Available element IDs in aiCreatedElementIds:`, Array.from(aiCreatedElementIds));
            
            // Additional debugging for connection-only scenarios
            if (noNewElements) {
              console.warn(`[handleCreateAIElements] No elements created in this call - checking if IDs exist in board state`);
              const fromInBoard = board.elements.find(e => e.id === fromId);
              const toInBoard = board.elements.find(e => e.id === toId);
              console.warn(`[handleCreateAIElements] From element in board:`, fromInBoard ? `Found (${fromInBoard.type})` : 'Not found');
              console.warn(`[handleCreateAIElements] To element in board:`, toInBoard ? `Found (${toInBoard.type})` : 'Not found');
            }
            
            return null;
          }

          console.log(`[handleCreateAIElements] Batch connection prepared: ${fromId} -> ${toId}${conn.label ? ` (${conn.label})` : ''}`);
          return {
            fromId,
            toId,
            isAiGenerated: true,
            ...(conn.label && { label: conn.label })
          };
        }).filter((conn): conn is { fromId: string; toId: string; isAiGenerated: boolean; label?: string } => conn !== null);

        console.log(`[handleCreateAIElements] Final batch connections to create:`, batchConnections);

        if (batchConnections.length > 0) {
          try {
            console.log(`[handleCreateAIElements] Calling addBatchConnections with ${batchConnections.length} connections`);
            const createdConnections = addBatchConnections(batchConnections);
            console.log(`[handleCreateAIElements] Successfully called addBatchConnections`);

            // If only connections were created (no new elements), focus viewport on the connections
            if (elements.length === 0 && createdConnections.length > 0) {
              console.log(`[handleCreateAIElements] ===== CONNECTION-ONLY OPERATION DETECTED =====`);
              console.log(`[handleCreateAIElements] Created connections:`, createdConnections.map(c => ({ id: c.id, fromId: c.fromId, toId: c.toId })));
              console.log(`[handleCreateAIElements] Scheduling viewport focus on connections in 300ms`);
              setTimeout(() => {
                console.log(`[handleCreateAIElements] Executing delayed connection focus`);
                focusOnConnection(createdConnections.map(c => c.id));
              }, 300); // Delay to ensure connections are rendered
            } else {
              console.log(`[handleCreateAIElements] Not a connection-only operation. Elements: ${elements.length}, Connections: ${createdConnections.length}`);
            }
          } catch (error) {
            console.error('[handleCreateAIElements] Error creating batch connections:', error);
            toast.error('Error creating some connections. Please check the board.');
          }
        } else {
          console.warn(`[handleCreateAIElements] No valid connections to create after filtering`);
        }
      }
      
      // Auto-adjust viewport to show newly created elements using original creation data
      if (elements.length > 0) {
        console.log(`Auto-adjusting viewport to show ${elements.length} created elements...`);

        // Prepare element data for viewport adjustment
        console.log(`[DEBUG] Original elements data:`, elements);
        const elementsForViewport = elements.map(element => {
          console.log(`[DEBUG] Processing element:`, element);
          console.log(`[DEBUG] position_x:`, element.position_x, `position_y:`, element.position_y);
          return {
            position: { x: element.position_x, y: element.position_y },
            type: element.type.toLowerCase()
          };
        });
        console.log(`[DEBUG] Elements for viewport:`, elementsForViewport);

        // Highlight newly created elements to provide visual feedback
        // Apply highlighting after viewport adjustment for better visual flow
        setTimeout(() => {
          if (createdIds.length > 0) {
            // For multiple elements, use multi-selection to highlight them all
            if (createdIds.length > 1) {
              setSelectedItemIds(createdIds);
              // Clear the highlight after 3 seconds
              setTimeout(() => {
                setSelectedItemIds([]);
              }, 3000);
            } else {
              // For single element, use regular selection
              selectItem(createdIds[0]);
              // Clear the highlight after 3 seconds
              setTimeout(() => {
                selectItem(null);
              }, 3000);
            }
          }
        }, 200); // Apply highlighting after viewport adjustment completes

        // Adjust viewport immediately for better user experience
        // Use a minimal delay to ensure elements are rendered in the DOM
        setTimeout(() => {
          adjustViewportToCreatedElements(elementsForViewport, 50);
        }, 100);
      }

      // Clean up aiCreatedElementIds after a delay to prevent memory leaks
      setTimeout(() => {
        setAiCreatedElementIds(prevAiIds => {
          const newAiIds = new Set(prevAiIds);
          createdIds.forEach(id => {
            newAiIds.delete(id);
          });
          return newAiIds;
        });
      }, 10000); // Clean up after 10 seconds
      
      // Fetch images for AI-generated articles that have URLs
      // Process these after connections are established so we don't block the initial display
      try {
        console.log("Starting image fetching for AI-generated articles...");
        
        // Build a map of created article items for quick lookup by ID
        const createdItemsMap = new Map(
          createdItems.map(item => [item.id, item])
        );
        
        // Go through each AI article element that has a URL
        const articleElements = elements.filter(e => e.type.toLowerCase() === 'article' && e.url);
        console.log(`Found ${articleElements.length} AI articles with URLs:`, articleElements.map(e => ({ id: e.id, url: e.url })));
        
        for (const articleElement of articleElements) {
          // Get the UUID mapped from the AI ID
          const articleId = idMap[articleElement.id];
          if (!articleId) {
            console.warn(`No mapped ID found for AI article: ${articleElement.id}`);
            continue;
          }
          
          // Get the created item directly from our createdItems (which we just added)
          // instead of looking it up in the potentially stale board.elements
          const articleItem = createdItemsMap.get(articleId);
          if (!articleItem) {
            console.warn(`Article item not found in created items: ${articleId}`);
            continue;
          }

          // Validate URL format before attempting to fetch metadata
          let urlToFetch = articleElement.url || '';
          
          // Try to fix URLs without protocol by adding https://
          if (!urlToFetch.startsWith('http://') && !urlToFetch.startsWith('https://')) {
            console.log(`URL missing protocol, adding https:// prefix: ${urlToFetch}`);
            urlToFetch = `https://${urlToFetch}`;
          }
          
          console.log(`Fetching metadata for AI article: ${articleId} with URL: ${urlToFetch}`);
          
          try {
            // Call extract-article endpoint to get metadata including image URL
            console.log(`Making extract-article request for: ${urlToFetch}`);
            const extractResponse = await fetch('/api/board/extract-article', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ url: urlToFetch })
            });
            
            if (!extractResponse.ok) {
              const errorText = await extractResponse.text();
              console.warn(`Failed to fetch metadata for article ${articleId}: ${extractResponse.status} ${extractResponse.statusText}`, errorText);
              continue;
            }
            
            const articleData = await extractResponse.json();
            console.log(`Article metadata fetched:`, articleData);
            
            // If we found an image URL, download it via the proxy API
            if (articleData.imageUrl) {
              console.log(`Found image for article ${articleId}: ${articleData.imageUrl}`);
              
              try {
                // Call proxy-image endpoint to save the image to storage
                console.log(`Making proxy-image request for: ${articleData.imageUrl}`);
                const proxyResponse = await fetch('/api/storage/proxy-image', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ imageUrl: articleData.imageUrl })
                });
                
                if (!proxyResponse.ok) {
                  const errorText = await proxyResponse.text();
                  console.warn(`Failed to proxy image for article ${articleId}: ${proxyResponse.status} ${proxyResponse.statusText}`, errorText);
                  continue;
                }
                
                const imageData = await proxyResponse.json();
                console.log(`Proxy image response:`, imageData);
                
                if (imageData.success && imageData.path) {
                  console.log(`Successfully saved image for article ${articleId}: ${imageData.path}`);
                  
                  let new_file_url: string | undefined = imageData.path;
                  // Safety check: if the returned 'path' from proxy-image is the same as the article's main URL (urlToFetch),
                  // then it's not a valid file_url for an image. Set to undefined.
                  if (imageData.path === urlToFetch) {
                    console.warn(`[DetectiveBoard] proxy-image returned the article's own URL ('${urlToFetch}') as 'imageData.path'. ` +
                                 `This is incorrect for an image file_url. Setting file_url for article ${articleId} to undefined.`);
                    new_file_url = undefined;
                  }

                  // Update the article with the image file URL and website_url
                  updateItem(articleId, {
                    file_url: new_file_url, // Use the potentially corrected file_url
                    imageUrl: articleData.imageUrl, // Keep the original URL for immediate display
                    website_url: articleData.website_url || articleData.url || urlToFetch // Add website_url if available
                  });
                  
                  // For diagnostic purposes, let's log the updated article
                  console.log(`Article ${articleId} updated with image data:`, {
                    file_url: new_file_url,
                    imageUrl: articleData.imageUrl
                  });
                  
                  // Notify the user of the successful image addition
                  toast.success(`Updated article with image from ${articleData.domain || 'website'}`, {
                    duration: 2000,
                    position: 'bottom-right'
                  });
                } else {
                  console.warn(`Proxy image response missing expected data for article ${articleId}`, imageData);
                }
              } catch (proxyError) {
                console.error(`Error in proxy step for article ${articleId}:`, proxyError);
              }
            } else {
              console.log(`No image URL found for article ${articleId} with metadata:`, articleData);
            }
          } catch (extractError) {
            console.error(`Error fetching metadata for article ${articleId}:`, extractError);
          }
        }
        
        console.log("Completed image fetching for AI articles");
      } catch (error) {
        console.error("Error fetching images for AI articles:", error);
        // Don't show an error toast as this is an enhancement, not critical functionality
      }
      
    } catch (error) {
      console.error('Error creating board elements or connections:', error); // Updated error message scope
      toast.error('Error creating some elements or connections. Please check the board.');
    }
    // Note: Removed automatic sidebar closing to allow continued AI interaction
  }, [addBatchItems, addBatchConnections, toast, updateItem, adjustViewportToCreatedElements, selectItem, setSelectedItemIds, board.elements, loadedItemIds, aiCreatedElementIds]);

  // Handle processing AI operations (update/delete)
  const handleProcessAIOperations = useCallback(async (operations: any[], operationType: 'update' | 'delete') => {
    try {
      console.log(`[handleProcessAIOperations] Processing ${operations.length} ${operationType} operations`);

      // Show operation feedback
      if (operationType === 'delete') {
        setOperationFeedback({
          isVisible: true,
          operationType: 'delete',
          message: `Deleting ${operations.length} item${operations.length > 1 ? 's' : ''}...`
        });
      } else if (operationType === 'update') {
        setOperationFeedback({
          isVisible: true,
          operationType: 'update',
          message: `Updating ${operations.length} item${operations.length > 1 ? 's' : ''}...`
        });
      }

      // Collect elements and connections to be deleted for visual feedback
      const elementsToDelete: string[] = [];
      const connectionsToDelete: string[] = [];

      operations.forEach(operation => {
        if (operation.type === 'element' && operation.elementId) {
          elementsToDelete.push(operation.elementId);
        } else if (operation.type === 'connection') {
          if (operation.connectionId) {
            connectionsToDelete.push(operation.connectionId);
          } else if (operation.fromElementId && operation.toElementId) {
            const connection = connections.find(conn =>
              conn.fromId === operation.fromElementId && conn.toId === operation.toElementId
            );
            if (connection) {
              connectionsToDelete.push(connection.id);
            }
          }
        }
      });

      // Apply pre-deletion visual feedback
      if (operationType === 'delete') {
        // Mark elements as deleting for visual feedback
        if (elementsToDelete.length > 0) {
          setDeletingElementIds(prev => new Set([...prev, ...elementsToDelete]));

          // Focus viewport on elements being deleted
          const elementsToFocus: ViewportElement[] = elementsToDelete
            .map(id => board.elements.find(e => e.id === id))
            .filter(Boolean)
            .map(element => ({
              position: element!.position,
              width: element!.width || 200,
              height: element!.height || 100,
              type: element!.type
            }));

          if (elementsToFocus.length > 0) {
            await focusOnElements(elementsToFocus, VIEWPORT_ANIMATION_PRESETS.deletion);
          }
        }

        if (connectionsToDelete.length > 0) {
          setDeletingConnectionIds(prev => new Set([...prev, ...connectionsToDelete]));

          // Focus viewport on connections being deleted (show connected elements)
          console.log(`[Connection Deletion] ===== FOCUSING ON CONNECTIONS BEING DELETED =====`);
          console.log(`[Connection Deletion] Connections to delete:`, connectionsToDelete);
          console.log(`[Connection Deletion] Calling focusOnConnection for deletion`);
          await focusOnConnection(connectionsToDelete);
          console.log(`[Connection Deletion] focusOnConnection completed for deletion`);
        }

        // Wait for visual feedback to be visible
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Process the actual operations
      for (const operation of operations) {
        if (operation.type === 'element') {
          if (operationType === 'update' && operation.elementId && operation.updates) {
            console.log(`[handleProcessAIOperations] Updating element ${operation.elementId}:`, operation.updates);
            await updateItem(operation.elementId, operation.updates);
          } else if (operationType === 'delete' && operation.elementId) {
            console.log(`[handleProcessAIOperations] Deleting element ${operation.elementId}`);
            await deleteItem(operation.elementId);
          }
        } else if (operation.type === 'connection') {
          if (operationType === 'delete') {
            if (operation.connectionId) {
              console.log(`[handleProcessAIOperations] Deleting connection ${operation.connectionId}`);
              await deleteConnection(operation.connectionId);
            } else if (operation.fromElementId && operation.toElementId) {
              console.log(`[handleProcessAIOperations] Deleting connection ${operation.fromElementId} -> ${operation.toElementId}`);
              // Find the connection by element IDs and delete it
              const connection = connections.find(conn =>
                conn.fromId === operation.fromElementId && conn.toId === operation.toElementId
              );
              if (connection) {
                await deleteConnection(connection.id);
              } else {
                console.warn(`[handleProcessAIOperations] Connection not found: ${operation.fromElementId} -> ${operation.toElementId}`);
              }
            }
          }
        }
      }

      // Handle post-operation actions
      if (operationType === 'delete') {
        // Clear deletion visual feedback
        setDeletingElementIds(new Set());
        setDeletingConnectionIds(new Set());
        setOperationFeedback(null);
      } else if (operationType === 'update') {
        // Focus viewport on updated elements and provide visual feedback
        const updatedElementIds = operations
          .filter(op => op.type === 'element' && op.elementId)
          .map(op => op.elementId);

        if (updatedElementIds.length > 0) {
          // Focus viewport on updated elements
          const elementsToFocus: ViewportElement[] = updatedElementIds
            .map(id => board.elements.find(e => e.id === id))
            .filter(Boolean)
            .map(element => ({
              position: element!.position,
              width: element!.width || 200,
              height: element!.height || 100,
              type: element!.type
            }));

          if (elementsToFocus.length > 0) {
            setTimeout(async () => {
              await focusOnElements(elementsToFocus, VIEWPORT_ANIMATION_PRESETS.update);

              // Apply visual feedback to updated elements
              applyVisualFeedback(
                updatedElementIds,
                selectItem,
                setSelectedItemIds,
                { duration: 2500, type: 'pulse' }
              );
            }, 200);
          }
        }

        // Clear operation feedback
        setTimeout(() => {
          setOperationFeedback(null);
        }, 1500);
      }

      console.log(`[handleProcessAIOperations] Successfully processed ${operations.length} ${operationType} operations`);
    } catch (error) {
      console.error(`[handleProcessAIOperations] Error processing ${operationType} operations:`, error);
      toast.error(`Failed to ${operationType} some items. Please try again.`);
    }
  }, [updateItem, deleteItem, deleteConnection, connections, toast]);

  // Check if the board is public
  const isBoardPublic = useMemo(() => {
    console.log("Board sharing data:", board?.sharing);
    
    if (board?.sharing && Array.isArray(board.sharing) && board.sharing.length > 0) {
      // Log each sharing record for debug
      board.sharing.forEach((share, index) => {
        console.log(`Sharing record ${index}:`, share);
      });
      
      // Check if any sharing record has public_board set to true
      const isPublic = board.sharing.some(share => {
        // Check the exact property
        return share.public_board === true;
      });
      
      return isPublic;
    }
    
    return false;
  }, [board.sharing]);

  // Handler for when public status changes
  const handlePublicStatusChange = useCallback(async (isPublic: boolean) => {
    console.log(`Attempting to set public status to: ${isPublic} for board: ${boardId}`);
    
    if (!boardId || boardId === 'new') {
      console.error('Cannot change public status: Invalid boardId');
      toast.error('Board must be saved before changing sharing settings.');
      return;
    }

    // Optimistic UI update (optional, can be moved after successful fetch)
    setBoard((currentBoard: Board) => {
      let newSharing = [...(currentBoard.sharing || [])];
      
      if (isPublic) {
        // If making public and no sharing records exist, create one
        if (newSharing.length === 0) {
          const newSharingRecord: BoardSharingRecord = {
            id: uuidv4(), // Generate a temporary ID
            board_id: currentBoard.id,
            public_board: true,
            likes: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          newSharing = [newSharingRecord];
        } else {
          // Update existing sharing records
          newSharing = newSharing.map(record => ({
            ...record,
            public_board: true,
            updated_at: new Date().toISOString()
          }));
        }
      } else {
        // If making private, update all sharing records
        newSharing = newSharing.map(record => ({
          ...record,
          public_board: false,
          updated_at: new Date().toISOString()
        }));
      }
      
      return {
        ...currentBoard,
        sharing: newSharing
      };
    });

    // --- Add API Call --- 
    try {
      const response = await fetch('/api/board/make-public', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          boardId,
          makePublic: isPublic // Send the new desired state
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error toggling board public status API:', errorData);
        toast.error(`Failed to update board status: ${errorData.error || 'Unknown error'}`);
        // TODO: Consider reverting the optimistic UI update here if needed
        return; // Stop execution if API call failed
      }

      const data = await response.json();
      console.log('Board public status changed successfully via API:', data);
      toast.success(`Board is now ${isPublic ? 'public' : 'private'}`);
      
      // Optional: Refetch board data or update local state based on API response if necessary,
      // although the optimistic update might be sufficient.
      
    } catch (error) {
      console.error('Error calling make-public API:', error);
      toast.error('An error occurred while updating sharing settings.');
      // TODO: Consider reverting the optimistic UI update here
    }
    // --- End API Call ---

  }, [setBoard, boardId, toast]); // Add boardId and toast to dependencies

  // Log the public status for debugging
  useEffect(() => {
    console.log("Board public status:", {
      isBoardPublic,
      sharingRecords: board?.sharing || []
    });
  }, [isBoardPublic, board.sharing]);

  // New handler to open the article image upload modal
  const handleRequestImageChange = (articleId: string) => {
    setEditingArticleId(articleId);
    setIsArticleImageModalOpen(true);
  };
  
  // New handler to close the article image upload modal
  const handleCloseArticleImageModal = () => {
    setIsArticleImageModalOpen(false);
    setEditingArticleId(null);
  };

  // New handler for when article image upload is complete
  const handleArticleImageUploadComplete = (articleId: string, imagePath: string, localImageUrl: string) => {
    console.log(`[DetectiveBoard] handleArticleImageUploadComplete: articleId=${articleId}, imagePath=${imagePath}, localImageUrl=${localImageUrl.substring(0, 50)}...`); // Log received data
    // Optimistic Update: Use local blob URL for immediate display
    // Store persistent path in file_url
    updateItem(articleId, { 
      imageUrl: localImageUrl, // Show this temporary URL immediately
      file_url: imagePath      // Store the actual path here
    }); 
    handleCloseArticleImageModal();
    toast.success("Article image updated successfully!");
  };

  // ------------------- Preview Capture Logic -------------------
  const previewTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastPreviewTimeRef = useRef<number>(0);

  // Schedule a preview capture, but throttle to once per minute
  const schedulePreviewCapture = useCallback(async () => {
    if (!boardId || boardId === 'new') return;

    const now = Date.now();
    // Throttle: If the last successful preview was less than a minute ago, do nothing.
    if (now - lastPreviewTimeRef.current < 60_000) {
      console.log('Preview capture throttled.');
      return;
    }

    // Debounce: If a preview is already scheduled, clear it to reset the timer.
    if (previewTimeoutRef.current) {
      clearTimeout(previewTimeoutRef.current);
    }

    console.log('Scheduling new preview capture...');
    previewTimeoutRef.current = setTimeout(async () => {
      try {
        // Use refs to get the latest board state without making the callback dependent on them
        const currentBoard = boardRef.current;
        const currentConnections = connectionsRef.current;
        
        console.log('Generating board SVG for preview...');
        const embedImages = currentBoard.elements.length < 30;
        
        const svgString = await generateBoardSVG(currentBoard, currentConnections, { 
            embedImages: embedImages // Only embed images for smaller boards
        });

        if (svgString) {
          const blob = new Blob([svgString], { type: 'image/svg+xml' });
          await uploadBoardPreview(blob, boardId);
          lastPreviewTimeRef.current = Date.now(); // Update timestamp on successful upload
        }
      } catch (err) {
        console.error('Error generating or uploading SVG board preview:', err);
      } finally {
        previewTimeoutRef.current = null; // Clear the ref after execution
      }
    }, 2000); // Wait 2 seconds after the last change to capture
  }, [boardId]); // Dependency is now stable

  // Trigger preview capture when board content changes
  useEffect(() => {
    // We only care when there is at least one element (avoid empty boards)
    if (board.elements.length > 0) {
      schedulePreviewCapture();
    }
  }, [board.elements, board.connections, board.strokes, schedulePreviewCapture]);

  // Effect to add/remove body class for no-scroll styling and apply dynamic styles
  useEffect(() => {
    document.body.classList.add('body-no-scroll-for-board');

    const adjustBodyStylesForBoard = () => {
      if (typeof window !== 'undefined') {
        document.body.style.height = `${window.innerHeight}px`;
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.top = '0';
        document.body.style.left = '0';
        document.body.style.width = '100%';
        document.documentElement.style.overflow = 'hidden';
      }
    };

    adjustBodyStylesForBoard(); // Initial adjustment

    window.addEventListener('resize', adjustBodyStylesForBoard);
    window.addEventListener('orientationchange', adjustBodyStylesForBoard);

    return () => {
      console.log('[DetectiveBoard] Unmounting and removing anti-scroll styles.');
      document.body.classList.remove('body-no-scroll-for-board');

      // Remove inline styles that were added by adjustBodyStylesForBoard
      document.body.style.removeProperty('height');
      document.body.style.removeProperty('overflow');
      document.body.style.removeProperty('position');
      document.body.style.removeProperty('top');
      document.body.style.removeProperty('left');
      document.body.style.removeProperty('width');
      document.documentElement.style.removeProperty('overflow');

      window.removeEventListener('resize', adjustBodyStylesForBoard);
      window.removeEventListener('orientationchange', adjustBodyStylesForBoard);
    };
  }, []); // Empty dependency array means this runs on mount and unmount

  // Function to handle downloading board data as JSON
  const handleDownloadBoardData = useCallback(() => {
    if (!board) {
      toast.error("Board data is not available.");
      return;
    }

    // Construct the data object to be downloaded
    const boardDataToDownload = {
      id: boardId,
      name: board.name,
      elements: board.elements,
      connections: connections, // Assuming connections are up-to-date from useBoard
      strokes: board.strokes,
      // Add any other relevant board properties you want to include
      // For example: lastSaved, scale, position, etc.
      lastSaved: lastSaved,
      scale: scale,
      position: position,
      createdAt: board.createdAt,
      updatedAt: board.updatedAt,
      sharing: board.sharing,
      boardDimensions: boardDimensions,
      // You might want to exclude very large or sensitive data if not needed for the export
    };

    const jsonString = JSON.stringify(boardDataToDownload, null, 2); // Pretty print JSON
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `board-data-${boardId || "untitled"}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("Board data downloaded as JSON.");
  }, [board, boardId, connections, lastSaved, scale, position, boardDimensions]);

  // Modify board items rendering to use lazy loading with animations
  const renderBoardItems = () => {
    // Check for duplicate IDs in board.elements
    const elementIds = board.elements.map(el => el.id);
    const duplicateIds = elementIds.filter((id, index) => elementIds.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      board.elements.forEach((el, index) => {
        if (duplicateIds.includes(el.id)) {
          console.error(`[renderBoardItems] Duplicate element ${index}: ${el.id} (${el.type})`);
        }
      });
    }

    // Create a modified completeConnection function that checks for text elements
    const handleConnectionComplete = (itemId: string) => {
      // Find the target item
      const targetItem = board.elements.find(item => item.id === itemId);

      // If it's a text element, do nothing
      if (targetItem && targetItem.type === 'text') {
        return;
      }

      // Otherwise, proceed with the normal connection completion
      completeConnection(itemId);
    };

    // Create a modified startConnection function that checks for text elements
    const handleConnectionStart = (itemId: string) => {
      // Find the source item
      const sourceItem = board.elements.find(item => item.id === itemId);

      // If it's a text element, do nothing
      if (sourceItem && sourceItem.type === 'text') {
        return;
      }

      // Otherwise, proceed with the normal connection start
      startConnection(itemId);
    };

    // Helper function to check if an item is part of a context rectangle
    const getItemContextInfo = (itemId: string) => {
      const contextRect = contextRectangles.find(rect => rect.itemIds.includes(itemId));
      return contextRect ? {
        isInContext: true,
        contextColor: contextRect.color,
        contextId: contextRect.id
      } : { isInContext: false, contextColor: undefined, contextId: undefined };
    };

    // Render items that have been marked as loaded OR are AI-created (to prevent disappearing during creation)
    const filteredElements = board.elements.filter(item =>
      loadedItemIds.has(item.id) || aiCreatedElementIds.has(item.id)
    );

    const renderedElements = filteredElements.map(item => {

        const contextInfo = getItemContextInfo(item.id);
        
        const commonProps = {
          id: item.id,
          content: item.content,
          position: item.position,
          width: item.width,
          height: item.height,
          onSizeChange: (id: string, w: number, h: number) => updateItem(id, { width: w, height: h }),
          onPositionChange: updateItemPositionWithContext,
          onSelect: selectItem,
          isSelected: selectedItemId === item.id,
          scale: scale,
          onDelete: deleteItem,
          onContentChange: updateItemContent,
          connectMode: connectMode,
          connectStart: connectStart,
          connections: connections,
          onConnectionStart: handleConnectionStart,
          onConnectionComplete: handleConnectionComplete,
          presentationMode: presentationMode,
          isMultiSelected: selectedItemIds.includes(item.id),
          isSelectionModeActive: isSelectionModeActive,
          // Context rectangle info for visual feedback
          isInContextGroup: contextInfo.isInContext,
          contextGroupColor: contextInfo.contextColor,
          contextGroupId: contextInfo.contextId,
          onHighlightContextGroup: handleHighlightContextGroup
        };

        // Wrap each rendered component with motion.div for animation
        const renderComponent = (component: React.ReactElement) => {
          const isDeleting = deletingElementIds.has(item.id);

          if (isDeleting) {
            return (
              <motion.div
                key={`${item.id}-deleting`}
                initial={{ opacity: 1, scale: 1 }}
                animate={{
                  opacity: 0.3,
                  scale: 0.95,
                  filter: 'grayscale(50%)'
                }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                style={{ pointerEvents: 'none' }}
              >
                {component}
              </motion.div>
            );
          }

          return component; // Return the component directly without the motion wrapper
        };

        if (presentationMode) {
          const readOnlyProps = {
            id: item.id,
            content: item.content,
            position: item.position,
            width: item.width,
            height: item.height,
            scale: scale,
          };

          if (item.type === 'sticky' || item.type.startsWith('sticky-')) {
            let color = item.color || 'yellow';
            if (item.type.startsWith('sticky-')) {
              color = item.type.split('-')[1];
            }
            return renderComponent(
              <StickyNoteReadOnly
                key={item.id}
                {...readOnlyProps}
                color={color}
              />
            );
          } else if (item.type === 'text') {
            return renderComponent(
              <TextNodeReadOnly
                key={item.id}
                {...readOnlyProps}
              />
            );
          } else if (item.type === 'article') {
            return renderComponent(
              <ArticleNodeReadOnly
                key={item.id}
                id={item.id}
                content={item.content}
                position={item.position}
                width={item.width ?? 300}
                height={item.height ?? 400}
                scale={scale}
                title={item.title || 'Untitled Article'}
                url={item.url || ''}
                website_url={item.website_url}
                imageUrl={item.imageUrl}
                file_url={item.file_url}
              />
            );
          } else if (isImageNode(item)) {
            return renderComponent(
              <ImageNodeReadOnly
                key={item.id}
                id={item.id}
                content={item.content}
                position={item.position}
                width={item.width ?? 250}
                height={item.height ?? 250}
                scale={scale}
                imageUrl={item.imageUrl}
              />
            );
          }
          return null;
        }

        if (item.type === 'sticky' || item.type.startsWith('sticky-')) {
          // Extract color from type if it's in the format sticky-{color}
          let color = item.color || 'yellow';
          if (item.type.startsWith('sticky-')) {
            color = item.type.split('-')[1];
          }
          
          return renderComponent(
            <StickyNote
              key={item.id}
              {...commonProps}
              color={color}
            />
          );
        } else if (item.type === 'text') {
          return renderComponent(
            <TextNode
              key={item.id}
              {...commonProps}
            />
          );
        } else if (item.type === 'article') {
          return renderComponent(
            <ArticleNode
              key={item.id}
              {...commonProps}
              title={item.title || 'Untitled Article'}
              url={item.url || ''}
              website_url={item.website_url}
              imageUrl={item.imageUrl}
              width={item.width ?? 300}
              height={item.height ?? 400}
              onTitleChange={(id, title) => updateItem(id, { title })}
              onRequestImageChange={handleRequestImageChange}
            />
          );
        } else if (isImageNode(item)) {
          return renderComponent(
            <ImageNode
              key={item.id}
              {...commonProps}
              imageUrl={item.imageUrl}
              file_url={item.file_url}
              alt={item.alt || ''}
              onVisibilityToggle={(id, isVisible) => handleVisibilityToggle(id, isVisible)}
              isVisible={item.type === 'image'}
            />
          );
        }
        return null;
      });

    return renderedElements;
  };

  const generateSelectedContext = useCallback(() => {
    if (!selectedItemIds.length) return '';
    const selectedItems = board.elements.filter(el => selectedItemIds.includes(el.id));
    let context = "Selected elements:";
    selectedItems.forEach(el => {
      context += `\n- ID: ${el.id}, type: '${el.type}', position: (${Math.round(el.position.x)}, ${Math.round(el.position.y)}), content: '${el.content?.substring(0,50) || el.title || 'No content'}'`;
    });
    const selectedConns = connections.filter(conn => selectedItemIds.includes(conn.fromId) && selectedItemIds.includes(conn.toId));
    if (selectedConns.length) {
      context += "\n\nConnections between selected elements:";
      selectedConns.forEach(conn => {
        context += `\n- From ${conn.fromId} to ${conn.toId}`;
      });
    }
    return context;
  }, [selectedItemIds, board.elements, connections]);

  const handleAddToChat = useCallback(() => {
    if (isAddingToChat) return; // Prevent multiple simultaneous calls
    
    try {
      setIsAddingToChat(true);
      
      // Validate prerequisites
      if (!selectedItemIds.length) {
        toast.error('No items selected. Please select items first.');
        throw new Error('No items selected');
      }
      
      if (!persistentSelectionRect) {
        toast.error('Selection rectangle not found. Please try selecting items again.');
        throw new Error('Selection rectangle not found');
      }

      if (!aiChatRef.current) {
        console.error('[DetectiveBoard] AI chat ref is not available');
        toast.error('AI chat is not ready. Please try again.');
        throw new Error('AI chat ref not available');
      }

      // Open AI chat sidebar if not already open
      if (!isAIChatSidebarOpen) {
        setIsAIChatSidebarOpen(true);
      }
      
      const contextId = `context-${Date.now()}`;
      const context = generateSelectedContext();
      
      // Add to context rectangles
      const newContextRect: ContextRectangle = {
        id: contextId,
        rect: { ...persistentSelectionRect },
        itemIds: [...selectedItemIds],
        color: contextColors[contextRectangles.length % contextColors.length]
      };
      setContextRectangles(prev => [...prev, newContextRect]);
      
      // Add context to chat with error handling
      aiChatRef.current.addContextMessage(context);
      
      // Clear current selection but keep the context rectangle
      setSelectedItemIds([]);
      setPersistentSelectionRect(null);
      
      // Success feedback
      toast.success(`Added ${selectedItemIds.length} items to chat context`);
      
    } catch (error) {
      console.error('[DetectiveBoard] Error adding context to chat:', error);
      toast.error('Failed to add items to chat. Please try again.');
    } finally {
      setIsAddingToChat(false);
    }
  }, [isAddingToChat, isAIChatSidebarOpen, generateSelectedContext, selectedItemIds, persistentSelectionRect, contextRectangles.length, setSelectedItemIds, setIsAIChatSidebarOpen]);

  const removeContextRectangle = useCallback((contextId: string) => {
    setContextRectangles(prev => prev.filter(rect => rect.id !== contextId));
    if (aiChatRef.current) {
      aiChatRef.current.removeContextItem(contextId);
    }
  }, []);

  // Handle highlighting context group items
  const handleHighlightContextGroup = useCallback((contextId: string) => {
    const contextRect = contextRectangles.find(rect => rect.id === contextId);
    if (contextRect) {
      // Temporarily select all items in the context group
      setSelectedItemIds(contextRect.itemIds);
      
      // Clear the selection after a brief delay
      setTimeout(() => {
        setSelectedItemIds([]);
      }, 1500);
    }
  }, [contextRectangles, setSelectedItemIds]);

  // Handle group movement for context rectangles
  const handleContextGroupMovement = useCallback((itemId: string, newPosition: Position, oldPosition: Position) => {
    // Find which context rectangle (if any) contains this item
    const contextRect = contextRectangles.find(rect => rect.itemIds.includes(itemId));
    
    if (!contextRect) {
      // Not part of a context rectangle, handle normally
      return false;
    }

    // Calculate the movement delta
    const deltaX = newPosition.x - oldPosition.x;
    const deltaY = newPosition.y - oldPosition.y;

    // If there's no movement, don't do anything
    if (deltaX === 0 && deltaY === 0) {
      return true; // Still handled by context group
    }



    // Move all other items in the context rectangle by the same delta
    contextRect.itemIds.forEach(id => {
      if (id !== itemId) { // Don't move the item that's already being moved
        const currentItem = board.elements.find(el => el.id === id);
        if (currentItem && currentItem.position) {
          const newItemPosition = {
            x: currentItem.position.x + deltaX,
            y: currentItem.position.y + deltaY
          };
          updateItemPosition(id, newItemPosition, { skipSync: false });
        }
      }
    });

    // Update the context rectangle position
    setContextRectangles(prev => prev.map(rect => {
      if (rect.id === contextRect.id) {
        return {
          ...rect,
          rect: {
            x: rect.rect.x + deltaX,
            y: rect.rect.y + deltaY,
            width: rect.rect.width,
            height: rect.rect.height
          }
        };
      }
      return rect;
    }));

    return true; // Indicates this movement was handled by context group
  }, [contextRectangles, board.elements, updateItemPosition]);

  // Create a wrapped version of updateItemPosition that handles context groups
  const updateItemPositionWithContext = useCallback((itemId: string, newPosition: Position, options?: { skipSync?: boolean }) => {
    // Get the current position before updating
    const currentItem = board.elements.find(el => el.id === itemId);
    if (!currentItem || !currentItem.position) {
      // Fallback to normal behavior if item not found
      updateItemPosition(itemId, newPosition, options);
      return;
    }

    const oldPosition = currentItem.position;

    // First update the item position normally
    updateItemPosition(itemId, newPosition, options);

    // Then handle group movement if this item is part of a context rectangle
    handleContextGroupMovement(itemId, newPosition, oldPosition);
  }, [board.elements, updateItemPosition, handleContextGroupMovement]);

  // Handle focus context event from sidebar
  useEffect(() => {
    const handleFocusContext = (event: CustomEvent) => {
      const contextId = event.detail;
      const contextRect = contextRectangles.find(rect => rect.id === contextId);
      if (contextRect && boardContainerRef.current) {
        // Calculate the center of the context rectangle
        const contextCenterX = contextRect.rect.x + contextRect.rect.width / 2;
        const contextCenterY = contextRect.rect.y + contextRect.rect.height / 2;
        
        // Get viewport dimensions
        const containerRect = boardContainerRef.current.getBoundingClientRect();
        const viewportCenterX = containerRect.width / 2;
        const viewportCenterY = containerRect.height / 2;
        
        // Calculate the new position to center the context rectangle in the viewport
        const newX = viewportCenterX - (contextCenterX * scale);
        const newY = viewportCenterY - (contextCenterY * scale);
        
        // Move the viewport to center on the context items
        setPosition({ x: newX, y: newY });
        
        // Also select the items to highlight them
        setSelectedItemIds(contextRect.itemIds);
        calculateSelectionBounds();
      }
    };

    window.addEventListener('focusContext', handleFocusContext as EventListener);
    return () => window.removeEventListener('focusContext', handleFocusContext as EventListener);
  }, [contextRectangles, setSelectedItemIds, calculateSelectionBounds, setPosition, scale]);

  // Clear context rectangles when sidebar is closed
  useEffect(() => {
    if (!isAIChatSidebarOpen) {
      setContextRectangles([]);
    }
  }, [isAIChatSidebarOpen]);

  return (
    <div className="relative h-screen w-screen overflow-hidden bg-gray-100 dark:bg-gray-900 flex flex-col">
        
        {/* Loading Overlay */}
        <LoadingOverlay />
        
        {/* Back Button with original style & loading state */}
        <div className="absolute top-4 left-4 z-50">
           <button
              onClick={handleBackClick}
              className="p-2 glass-morphism rounded-full text-white hover:bg-noir-50 transition-colors"
              aria-label="Back to dashboard"
              disabled={isBackLoading}
           >
              {isBackLoading ? (
                <Loader2 size={20} className="animate-spin" />
              ) : (
                <ArrowLeft size={20} />
              )}
           </button>
        </div>

        {/* Zoom Slider */}
        {setScale && (
          <ZoomSlider
            scale={scale}
            setScale={setScale}
            minScale={MIN_SCALE}
            maxScale={MAX_SCALE}
            presentationMode={presentationMode}
          />
        )}

        {/* Top Right Controls */}
        <div 
          className="absolute top-4 z-50 flex space-x-2"
          style={{
            right: isAIChatSidebarOpen ? `${aiSidebarWidth + 16}px` : '1rem',
            transition: 'right 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
          }}
        >
           <button
              onClick={handleToggleAIChat}
              className="p-2 glass-morphism rounded-full text-white hover:bg-noir-50 transition-colors"
              aria-label="AI Research Assistant"
           >
              <Sparkles size={20} />
           </button>
           <button
              onClick={togglePresentationMode}
              className="p-2 glass-morphism rounded-full text-white hover:bg-noir-50 transition-colors"
              aria-label={presentationMode ? "Switch to normal mode" : "Switch to presentation mode"}
           >
              {presentationMode ? (
                <Eye size={20} />
              ) : (
                <EyeOff size={20} />
              )}
           </button>
        </div>

        {/* Toolbar - only show when not in presentation mode */}
        {!presentationMode && (
          <Toolbar
            onAddItem={addItem}
            onToggleConnectMode={toggleConnectMode}
            isConnectMode={connectMode}
            onSave={openSaveModal}
            lastSaved={lastSaved}
            boardName={board.name}
            onTogglePenMode={togglePenMode}
            isPenModeActive={isPenModeActive}
            openArticleForm={openArticleForm}
            openImageUploadModal={openImageUploadModal}
            penColor={penColor}
            penStrokeWidth={penStrokeWidth}
            onPenColorChange={setPenColor}
            onPenStrokeWidthChange={setPenStrokeWidth}
            isErasing={isErasing}
            onToggleEraserMode={toggleEraserMode}
            onGrabModeToggle={togglePenMode}
            isPublic={isBoardPublic}
            onPublicStatusChange={handlePublicStatusChange}
            boardId={boardId}
            isSelectionModeActive={isSelectionModeActive}
            onToggleSelectionMode={toggleSelectionMode}
            onDownloadBoardData={handleDownloadBoardData}
          />
        )}

        {/* Main Board Area */}
        <div 
          className="flex-1 relative"
          style={{
            marginRight: isAIChatSidebarOpen ? `${aiSidebarWidth}px` : '0px',
            transition: 'margin-right 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
          }}
        >
          <BoardCanvas
            viewState={boardViewState}
            onWheel={handleWheel}
            onCanvasPointerDown={handleBoardPointerDown}
            onCanvasPointerMove={isPenModeActive ? handlePenPointerMove : (isSelectionModeActive ? handleSelectionPointerMove : undefined)}
            onCanvasPointerUp={isPenModeActive ? handlePenPointerUp : (isSelectionModeActive ? handleSelectionPointerUp : undefined)}
            containerRef={boardContainerRef}
            isPenModeActive={isPenModeActive}
            isErasing={isErasing}
            boardDimensions={boardDimensions}
            connectStartItemPosition={connectStartItemPosition}
            hoveredStrokeId={hoveredStrokeId}
            isSelectionModeActive={isSelectionModeActive}
          >
            {/* Render Items Container */}
            <div data-testid="board-items-container" style={{ position: 'relative', width: '100%', height: '100%' }}>
                {renderBoardItems()}
                {/* Render Connections */}
                {renderConnections()}
                {/* Render Strokes */}
                <StrokeRenderer 
                   strokes={board.strokes} 
                   scale={scale} 
                   currentStroke={currentStroke}
                   hoveredStrokeId={hoveredStrokeId}
                />
                {/* Remote cursors */}
                <RemoteCursors cursors={remoteCursors} />

                {/* Render Selection Rectangle */}
                {isSelecting && selectionRect && (
                  <div
                    style={{
                      position: 'absolute',
                      left: `${selectionRect.x}px`,
                      top: `${selectionRect.y}px`,
                      width: `${selectionRect.width}px`,
                      height: `${selectionRect.height}px`,
                      border: '1px dashed #007bff',
                      backgroundColor: 'rgba(0, 123, 255, 0.1)',
                      pointerEvents: 'none',
                      zIndex: 1000,
                    }}
                  />
                )}

                {/* Render Persistent Selection Rectangle */}
                {persistentSelectionRect && selectedItemIds.length > 0 && (
                  <div
                    style={{
                      position: 'absolute',
                      left: `${persistentSelectionRect.x}px`,
                      top: `${persistentSelectionRect.y}px`,
                      width: `${persistentSelectionRect.width}px`,
                      height: `${persistentSelectionRect.height}px`,
                      border: '2px solid rgba(59, 130, 246, 0.8)',
                      backgroundColor: 'rgba(59, 130, 246, 0.1)',
                      pointerEvents: 'none',
                      zIndex: 999,
                      borderRadius: '4px',
                    }}
                  >
                    <div style={{ position: 'absolute', top: '-32px', left: '50%', transform: 'translateX(-50%)', pointerEvents: 'auto' }}>
                      <Button 
                        onClick={handleAddToChat}
                        size="sm" 
                        disabled={isAddingToChat}
                        className="flex items-center space-x-1"
                      >
                        {isAddingToChat ? (
                          <>
                            <Loader2 size={14} className="animate-spin" />
                            <span>Adding...</span>
                          </>
                        ) : (
                          'Add to chat'
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Render Context Rectangles */}
                {contextRectangles.map((contextRect, index) => (
                  <div
                    key={contextRect.id}
                    style={{
                      position: 'absolute',
                      left: `${contextRect.rect.x}px`,
                      top: `${contextRect.rect.y}px`,
                      width: `${contextRect.rect.width}px`,
                      height: `${contextRect.rect.height}px`,
                      border: `2px solid ${contextRect.color}`,
                      backgroundColor: `${contextRect.color}20`,
                      pointerEvents: 'none',
                      zIndex: 998 - index,
                      borderRadius: '4px',
                    }}
                  >
                    <div style={{ position: 'absolute', top: '-32px', right: '0px', pointerEvents: 'auto' }}>
                      <Button 
                        onClick={() => removeContextRectangle(contextRect.id)} 
                        size="sm" 
                        variant="destructive"
                      >
                        ×
                      </Button>
                    </div>
                    <div 
                      style={{ 
                        position: 'absolute', 
                        top: '-32px', 
                        left: '0px', 
                        padding: '2px 6px', 
                        backgroundColor: contextRect.color, 
                        color: 'white', 
                        fontSize: '10px', 
                        borderRadius: '3px',
                        pointerEvents: 'none'
                      }}
                    >
                      Context {index + 1}
                    </div>
                  </div>
                ))}
            </div>
          </BoardCanvas>
          
          {/* Loading Indicator */}
          <LoadingIndicator />
        </div>

        {/* Modals */}
        <SaveModal
          isOpen={modalState.showSaveModal}
          onClose={closeSaveModal}
          onSave={handleSaveFormSubmit}
          initialBoardName={board.name}
          isNewBoard={isNewBoard}
        />
        <ExitConfirmation
          isOpen={modalState.showExitConfirmation}
          onClose={closeExitConfirmation}
          onSaveAndExit={handleSaveBeforeExit}
          onExitWithoutSaving={handleExitWithoutSaving}
          destination={modalState.exitDestination}
        />
         <ArticleForm
          isOpen={modalState.showArticleForm}
          onClose={closeArticleForm}
          onSubmit={handleArticleSubmit}
          initialData={modalState.articleForm}
        />
        <ImageUploadModal
            isOpen={modalState.showImageUploadModal}
            onClose={closeImageUploadModal}
            onSubmit={handleImageUploadSubmit}
        />
        {modalState.showArticleForm && (
          <ArticleForm
            isOpen={modalState.showArticleForm}
            onClose={closeArticleForm}
            onSubmit={handleArticleSubmit}
            initialData={modalState.articleForm}
          />
        )}
        {isArticleImageModalOpen && (
          <ArticleImageUploadModal
            isOpen={isArticleImageModalOpen}
            onClose={handleCloseArticleImageModal}
            articleId={editingArticleId}
            onUploadComplete={handleArticleImageUploadComplete}
          />
        )}
        <AIChatSidebar
          ref={aiChatRef}
          isOpen={isAIChatSidebarOpen}
          onClose={handleCloseAIChat}
          onAddInsight={handleAddAIInsight}
          onCreateElements={handleCreateAIElements}
          onProcessAIOperations={handleProcessAIOperations}
          boardId={boardId}
          sidebarWidth={aiSidebarWidth}
          onResizeStart={handleAISidebarResizeStart}
        />

        {/* Operation Feedback Toast */}
        {operationFeedback && (
          <OperationToast
            operationType={operationFeedback.operationType}
            message={operationFeedback.message}
            isVisible={operationFeedback.isVisible}
            onClose={() => setOperationFeedback(null)}
          />
        )}
      </div>
  );
};

export default DetectiveBoard; 